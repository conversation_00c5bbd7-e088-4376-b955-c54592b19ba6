import {
    _decorator,
    Component,
    Node,
    Prefab,
    UITransform,
    Tween,
    v3,
    Label,
    UIOpacity,
    Sprite,
    Color,
    Edit<PERSON><PERSON>,
    director,
    Input,
} from 'cc'
import {
    Holder,
    IElement,
    ReleaseEvent,
    ReleaseManager,
    ReleaseState,
    ScrollAdapter,
    View,
    ViewManager,
} from '../adapter'
import { ChatItem } from './ChatItem'
import { cat } from '@/core/manager'
import { RoomEventConstant } from '@/core/business/constant'
import { RoomMessageEntity } from '@/core/business/yunxin/Entity'
import store from '@/core/business/store'
import { Room } from 'sgc'
import { GameCodeConstant } from '@/core/business/constant/GameCodeConstant.b'
const { ccclass, property } = _decorator
export interface IChatModel {
    type: number
    name: string
    message: string
    avatar: string //头像url
    from?: string
    idClient: string
}

@ccclass('ChatRoom')
export class ChatRoom extends ScrollAdapter<IChatModel> {
    @property(Prefab) chatPrefab: Prefab | null = null
    @property(EditBox) input: EditBox | null = null

    private retryCount = 0
    private needCheck = true //页面跳转时，正好有人发消息时，偶儿会丢消息。原因：YunXinSDK里收到了消息，但是chatroom初始化未完成，接收不到新消息。
    private keyboardHeight = 0

    public getPrefab(data: IChatModel): Node | Prefab {
        return this.chatPrefab!
    }
    public override getHolder(node: Node, code: string): Holder<IChatModel> {
        return new myHolder(node, code, this)
    }
    public override getView(): View<IChatModel> {
        return new myView(this)
    }
    public override initElement(element: IElement, data: any): void {}

    override onLoad() {
        console.log('ChatRoom-->onLoad()')
        // cat.YXSDK.enterRoom()
    }

    protected override onDestroy(): void {
        console.log('ChatRoom-->onDestroy()')
        // cat.YXSDK.exitRoom()
        cat.event.deleteEventByComponent(this)

        this.unregisterKeyboardEvent()
    }

    override start() {
        console.log('ChatRoom-->start() enter')
        this.queryMessageHistory()

        console.log(
            'ChatRoom-->start(), start on RoomEventConstant.ROOM_MESSAGE_INCOMING'
        )
        cat.event.on(
            RoomEventConstant.ROOM_MESSAGE_INCOMING,
            this.onRoomMessageIncoming.bind(this),
            this
        )

        if (GameCodeConstant.GAME_CODE !== ('sf_dixit' as any)) {
            cat.event.on(
                RoomEventConstant.HIDE_KEYBOARD,
                this.hideKeyboard.bind(this),
                this
            )
        }

        this.input?.node.on(
            Input.EventType.TOUCH_START,
            this.showKeyboard,
            this
        )

        this.registerKeyboardEvent()
    }

    private async hideKeyboard() {
        if (this.keyboardHeight <= 0) {
            return
        }

        if (this.input) {
            this.input.string = ''
        }
        wx.updateKeyboard({
            value: '',
            complete: () => {
                wx.hideKeyboard()
            },
        })
    }

    async queryMessageHistory() {
        let msgs: RoomMessageEntity[] = []
        try {
            msgs = await cat.YXSDK.queryHistoryMessage()
        } catch (error) {
            console.log('ChatRoom-->queryHistoryMessage(), error:', error)
            if (
                (error as any).error.code === 'No_connected' &&
                this.retryCount < 10
            ) {
                console.log('ChatRoom-->queryHistoryMessage(), error, retry')
                this.scheduleOnce(() => {
                    this.retryCount += 1
                    this.queryMessageHistory()
                }, 0.3)
            }
            return
        }

        console.log('ChatRoom-->queryHistoryMessage(), msgs:', msgs)

        if (!msgs || msgs.length === 0) {
            console.log(
                'ChatRoom-->queryHistoryMessage(), msgs is empty, return',
                msgs
            )
            return
        }

        await this.filterAndInsert(msgs)

        if (this.needCheck) {
            this.needCheck = false
            this.scheduleOnce(async () => {
                console.log('ChatRoom-->queryHistoryMessage(), needCheck')
                this.queryMessageHistory()
            }, 1)
        }
    }

    async onSend() {
        console.log('ChatRoom-->onSend: enter')
        if (
            !this.input ||
            this.input.string.length == 0 ||
            this.input.string.trim().length == 0
        ) {
            cat.gui.showToast({ title: '请输入内容!' })
            return
        }

        //调用云信发送
        const result = await cat.YXSDK.sendTextMsg(this.input.string)
        if (!result) {
            return
        }

        var data: IChatModel = {
            name: store.user.currentUserName,
            message: this.input!.string,
            type: 0,
            from: store.user.currentUserId,
            avatar: store.user.currentUserAvatar,
            idClient: '',
        }
        this.modelManager.insert(data, 0)
        if (this.input) {
            this.input.string = ''
        }
    }

    async onRoomMessageIncoming(roomMessageEntity: RoomMessageEntity) {
        if (!store.room.isExistInUserInfos(roomMessageEntity.from || '')) {
            await cat.api.room.userInfos({
                accessToken: store.login.access_token,
                chatUids: roomMessageEntity.from,
            })
        }
        const userInfo = store.room.getUserInfoByUserId(
            roomMessageEntity.from || ''
        )
        var data: IChatModel = {
            name: (userInfo as any)?.nickName || '',
            message: roomMessageEntity.body || '',
            type: 0,
            avatar: (userInfo as any)?.avatar || '',
            idClient: roomMessageEntity.idClient || '',
        }
        console.log('onRoomMessageIncoming-->insert:', data)
        this.modelManager.insert(data, 0)
    }

    showKeyboard() {
        if (GameCodeConstant.GAME_CODE === ('sf_dixit' as any)) {
            wx.showKeyboard({
                defaultValue: '',
                maxLength: 60,
                multiple: false,
                confirmHold: false,
                confirmType: 'done',
            })
        } else {
            wx.showKeyboard({
                defaultValue: '',
                maxLength: 60,
                multiple: false,
                confirmHold: false,
                confirmType: 'send',
            })
        }
    }
    registerKeyboardEvent() {
        if (GameCodeConstant.GAME_CODE === ('sf_dixit' as any)) return

        wx.onKeyboardConfirm(() => {
            this.onSend()
        })

        wx.onKeyboardHeightChange((res) => {
            this.keyboardHeight = res.height
            console.log('ChatRoom-->keyboardHeight:', res.height)
        })
    }

    unregisterKeyboardEvent() {
        if (GameCodeConstant.GAME_CODE === ('sf_dixit' as any)) return

        wx.offKeyboardConfirm()
        wx.offKeyboardHeightChange()
    }

    //将查询的结果跟this.modelManager.modelList去重比较，没有的插入到this.modelManager中
    private async filterAndInsert(historyArray: RoomMessageEntity[]) {
        let msgs: RoomMessageEntity[] = []
        historyArray.forEach((item) => {
            const find = this.modelManager.modelList.find(
                (model) => model.data.idClient === item.idClient
            )
            if (!find) {
                msgs.push(item)
            }
        })

        if (msgs.length <= 0) {
            console.log('ChatRoom-->filterAndInsert(), msgs is empty,return')
            return
        }

        //取msg列表里的from字段，去重后，用','拼接
        const froms = msgs
            .map((msg) => msg.from)
            .filter((v, i, a) => a.indexOf(v) === i)
            .join(',')

        console.log('ChatRoom-->filterAndInsert(), froms:', froms)
        await cat.api.room.userInfos({
            accessToken: store.login.access_token,
            chatUids: froms,
        })

        var dataArray: IChatModel[] = []
        for (let i = 0; i < msgs.length; i++) {
            const userInfo = store.room.getUserInfoByUserId(msgs[i].from || '')
            var data: IChatModel = {
                name: (userInfo as any)?.nickName || '',
                message: msgs[i].body || '',
                type: 0,
                avatar: (userInfo as any)?.avatar || '',
                idClient: msgs[i].idClient || '',
            }
            dataArray.push(data)
        }
        this.modelManager.insert(dataArray, 0)
    }
}

class myView extends View<IChatModel, ChatRoom> {
    protected onVisible(): void {}
    protected onDisable(): void {}
}
class myHolder extends Holder<IChatModel, ChatRoom> {
    private _chatItem: ChatItem | null = null
    protected onCreated(): void {
        this._chatItem = this.node.getComponent(ChatItem)
    }
    protected onVisible(): void {
        this._chatItem && this._chatItem.show(this)
    }
    protected onDisable(): void {
        this._chatItem && this._chatItem.hide()
    }
}
