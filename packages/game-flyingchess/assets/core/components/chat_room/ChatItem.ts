import {
    _decorator,
    Component,
    Node,
    Label,
    UITransform,
    Widget,
    Sprite,
} from 'cc'
import { Holder } from '../adapter'
import { IChatModel } from './ChatRoom'
import { cat } from '@/core/manager'
const { ccclass, property } = _decorator
@ccclass('ChatItem')
export class ChatItem extends Component {
    @property(Label) nameLabel: Label | null = null
    @property(Label) messageLabel: Label | null = null
    @property(UITransform) messageBg: UITransform | null = null
    @property(Sprite) avatar: Sprite | null = null
    private _holder: Holder<IChatModel> | null = null
    private _messageLabelTr: UITransform | null = null
    override onLoad() {
        const tr = this.messageLabel?.getComponent(UITransform)
        this._messageLabelTr = tr ? tr : null
    }
    override onEnable() {
        this.messageLabel?.node.on(
            Node.EventType.SIZE_CHANGED,
            this.onMessageSizeChanged,
            this
        )
    }
    override onDisable() {
        this.messageLabel?.node.off(
            Node.EventType.SIZE_CHANGED,
            this.onMessageSizeChanged,
            this
        )
    }
    show(holderParam: Holder<IChatModel>) {
        this._holder = holderParam
        if (this._messageLabelTr) this._messageLabelTr.width = 0
        const nameLabel = this.nameLabel
        const h = this._holder
        const data = h ? h.data : null
        if (nameLabel && data) nameLabel.string = String(data.name)
        this.showMessage()
        this.showAvatar()
    }
    hide() {
        if (this._messageLabelTr) this._messageLabelTr.width = 0
        if (this.messageBg) this.messageBg.width = 0
    }

    showMessage() {
        if (!this.messageLabel) return
        this.messageLabel.overflow = Label.Overflow.NONE
        const messageLabel = this.messageLabel
        const h = this._holder
        const data = h ? h.data : null
        if (messageLabel && data) messageLabel.string = data.message
    }

    showAvatar() {
        const h = this._holder
        const data = h ? h.data : null
        if (this.avatar && data && data.avatar) {
            cat.util.nodeUtils.setSpriteFrameToSprite(
                data.avatar,
                this.avatar,
                false
            )
        }
    }

    onMessageSizeChanged() {
        if (!this._holder || !this.node.active) return console.error('没有显示')
        var maxWidth = this._holder.adapter.crossAxisSize - 200
        if (!this._messageLabelTr) return
        if (this._messageLabelTr.width > maxWidth) {
            if (this.messageLabel)
                this.messageLabel.overflow = Label.Overflow.RESIZE_HEIGHT
            this._messageLabelTr.width = maxWidth
            return
        }
        // 马勒戈壁 这里修改不起作用，必须延迟一帧
        // this.messageBg.setContentSize(this._messageLabelTr.width, this._messageLabelTr.height)
        this.scheduleOnce(() => {
            console.log('修改背景')
            if (this.messageBg && this._messageLabelTr) {
                this.messageBg.setContentSize(
                    this._messageLabelTr.width + 40,
                    this._messageLabelTr.height + 30
                )
                if (this._holder && this._holder.transform)
                    this._holder.transform.height = Math.max(
                        this.messageBg.height,
                        100
                    )
                // 不手动调用也不会更新
                const w = this.messageBg.getComponent(Widget)
                w && w.updateAlignment()
            }
        })
    }
}
