/**
 * @describe 头部工具组件
 * <AUTHOR> AI
 * @date 2025-09-19
 *
 * 监听 store.global 里的当前场景，判断不为 loading，才展示
 */

import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { _decorator, Node } from 'cc'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

export type HeaderToolProps = {}

export type HeaderToolData = {}

@ccclass('HeaderTool')
export class HeaderTool extends BaseComponent<HeaderToolProps, HeaderToolData> {
    protected override onLoad(): void {}

    override onDestroy(): void {}

    protected override initUI(): void {
        // 初始设置显示状态
        this.updateVisibility()
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.global.currentScene,
            (currentScene) => {
                this.updateVisibility()
            },
            { fireImmediately: false }
        )
    }

    /**
     * 根据当前场景更新组件显示状态
     */
    private updateVisibility(): void {
        const currentScene = store.global.currentScene
        const shouldShow = !['start', 'loading', 'loading_wx'].includes(
            currentScene || ''
        )

        this.node.active = shouldShow
    }
}
