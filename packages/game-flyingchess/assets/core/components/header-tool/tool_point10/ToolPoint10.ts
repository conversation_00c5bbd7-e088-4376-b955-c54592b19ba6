import { AD, ADName } from '@/core/business/adver'
import { audioEffect } from '@/core/business/hooks/Decorator'
import { cat } from '@/core/manager'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { _decorator, Label, Node, tween, Vec3, UIOpacity } from 'cc'
import store from '@/core/business/store'
import { RoomEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

export type ToolPoint10Props = {}

export type ToolPoint10Data = {}

@ccclass('ToolPoint10')
export class ToolPoint10 extends BaseComponent<
    ToolPoint10Props,
    ToolPoint10Data
> {
    @property({ type: Node, tooltip: '小鱼干节点' })
    point: Node

    @property({ type: Label, tooltip: '小鱼干数量' })
    point_label: Label

    @property({ type: Node, tooltip: '提示浮窗' })
    popup: Node

    /** 点击时是否显示激励广告 */
    showReward = false

    private timerId = 0
    private CONST_TIME = 15000

    protected override onLoad(): void {}

    override onDestroy(): void {
        clearTimeout(this.timerId)
    }

    protected override initUI(): void {
        this.point.on(Node.EventType.TOUCH_START, this.onShowWXReward, this)
        this.popup.on(Node.EventType.TOUCH_START, this.onShowWXReward, this)
    }

    protected override addListener(): void {
        cat.event.on(
            RoomEventConstant.HIDE_POINT_POPUP,
            this.playHideAnimation,
            this
        )
    }

    protected override removeListener(): void {
        cat.event.off(
            RoomEventConstant.HIDE_POINT_POPUP,
            this.playHideAnimation,
            this
        )
    }

    protected override start(): void {
        this.popup.active = false
    }

    startTimer(): void {
        clearTimeout(this.timerId)
        if (store.user.isTodayAd) return

        this.timerId = setTimeout(() => {
            this.playShowAnimation()
        }, this.CONST_TIME)
    }

    /**
     * 显示动画
     */
    private playShowAnimation(): void {
        if (!this.showReward) return
        //当自己弹出时，关闭其他的弹窗
        cat.event.dispatchEvent(RoomEventConstant.HIDE_TRAFFIC_GAME)

        //让this.popup 水平上跟this.point_label 的位置一致
        let x = this.point_label.node.position.x
        this.popup.setPosition(x - 40, this.popup.position.y)

        this.popup.active = true
        const uiOpacity =
            this.popup.getComponent(UIOpacity) ||
            this.popup.addComponent(UIOpacity)

        tween(this.popup)
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start()

        tween(uiOpacity).to(0.2, { opacity: 255 }).start()
    }

    /**
     * 隐藏动画
     */
    private playHideAnimation(callback?: () => void): void {
        const uiOpacity = this.popup.getComponent(UIOpacity)

        tween(this.popup)
            .to(0.2, { scale: new Vec3(0.8, 0.8, 1) })
            .start()

        if (uiOpacity) {
            tween(uiOpacity)
                .to(0.2, { opacity: 0 })
                .call(() => {
                    if (callback) callback()
                })
                .start()
        } else {
            if (callback) callback()
        }
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.user.user_info.pointD10,
            (pointD10) => {
                //如果pointD10大于10000，则转换为1.xx
                let tempString = ''
                if (pointD10) {
                    tempString =
                        pointD10 > 10000
                            ? (pointD10 / 10000).toFixed(2) + 'w'
                            : pointD10.toString()
                } else {
                    tempString = '0'
                }

                if (tempString.length <= 2) {
                    tempString = ' ' + tempString + ' '
                }
                this.point_label.string = tempString
            },
            { fireImmediately: true }
        ).addReaction(
            () => store.global.currentScene,
            (currentScene) => {
                this.updateEnableReward()
            },
            { fireImmediately: true }
        )
    }

    updateEnableReward(): void {
        console.log(
            'ToolPoint10-->updateEnableReward(), currentScene:',
            store.global.currentScene
        )
        this.showReward = store.global.currentScene === 'lobby'
        if (!this.showReward) {
            this.popup.active = false
        } else {
            this.startTimer()
        }
    }

    @audioEffect()
    private async onShowWXReward() {
        this.playHideAnimation(() => {
            this.popup.active = false
        })

        console.log(
            'ToolPoint10-->onShowWXReward(), showReward:',
            this.showReward
        )
        //目前只有lobby.scene 支持激励视频
        if (!this.showReward) return

        const adsSerial = await cat.api.user.start_reward_ad({
            accessToken: store.login.access_token,
        })

        if (!adsSerial) {
            console.log('ToolPoint10-->onShowWXReward(), adsSerial:', adsSerial)
            return
        }

        //如果支持微信激励视频
        AD?.showRewardedAdByAdUnitId(ADName.REWARDED, async () => {
            store.user.setTodayAd()
            //用户看完广告，下发奖励（如积分、权限）
            await cat.api.user.end_reward_ad({
                accessToken: store.login.access_token,
                adsSerial: adsSerial,
            })

            //更新用户小鱼干
            await cat.api.user.get_user_info()
        })
    }
}
