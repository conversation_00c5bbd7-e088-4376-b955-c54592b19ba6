/**
 * @describe 导量游戏入口组件
 * <AUTHOR>
 * @date 2025-09-19
 *
 * 根据导量游戏列表长度控制显示隐藏
 */

import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { _decorator, Button, instantiate, Prefab, Node } from 'cc'
import store from '@/core/business/store'
import { cat } from '@/core/manager'
import { TrafficGames } from './TrafficGames'

const { ccclass, property } = _decorator

export type TrafficGamesEntryProps = {}

export type TrafficGamesEntryData = {
    /** TrafficGames组件节点 */
    trafficGamesNode?: Node
}

@ccclass('TrafficGamesEntry')
export class TrafficGamesEntry extends BaseComponent<
    TrafficGamesEntryProps,
    TrafficGamesEntryData
> {
    /** 主按钮 */
    @property({ type: Button, tooltip: '主按钮' })
    btn_main: Button

    /** TrafficGames预制体 */
    @property({ type: Prefab, tooltip: 'TrafficGames预制体' })
    prefab_traffic_games: Prefab

    /** TrafficGames容器节点 */
    @property({ type: Node, tooltip: 'TrafficGames容器节点' })
    trafficGamesContainer: Node

    /** TrafficGames组件实例 */
    private trafficGames: TrafficGames | null = null

    protected override initUI(): void {
        console.log('TrafficGamesEntry-->initUI()')
        this.loadTrafficGames()
        this.updateVisibility()
        this.initButtonEvent()
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.user.trafficGames,
            (_gameList) => {
                this.updateVisibility()
            },
            { fireImmediately: false }
        )
    }

    /**
     * 获取导量游戏列表
     */
    private async loadTrafficGames(): Promise<void> {
        console.log('TrafficGamesEntry-->loadTrafficGames()')
        try {
            await cat.api.user.get_traffic_games()
            console.log(
                'TrafficGamesEntry-->loadTrafficGames():',
                store.user.trafficGames
            )
        } catch (error) {
            console.warn('TrafficGamesEntry-->loadTrafficGames() error:', error)
        }
    }

    /**
     * 根据导量游戏列表长度更新组件显示状态
     */
    private updateVisibility(): void {
        const hasTrafficGames =
            store.user.trafficGames && store.user.trafficGames.length > 0
        this.node.active = hasTrafficGames
    }

    /**
     * 初始化按钮事件
     */
    private initButtonEvent(): void {
        if (this.btn_main) {
            this.btn_main.node.on(
                Button.EventType.CLICK,
                this.onMainButtonClick,
                this
            )
        }
    }

    /**
     * 主按钮点击事件
     */
    private onMainButtonClick(): void {
        this.showTrafficGames()
    }

    /**
     * 显示TrafficGames组件
     */
    private showTrafficGames(): void {
        if (!this.trafficGames) {
            this.createTrafficGames()
        }

        if (this.trafficGames) {
            this.trafficGames.toggle()
        }
    }

    /**
     * 创建TrafficGames组件
     */
    private createTrafficGames(): void {
        if (!this.prefab_traffic_games) {
            console.warn('TrafficGamesEntry: 未设置TrafficGames预制体')
            return
        }

        try {
            const node = instantiate(this.prefab_traffic_games)
            this.trafficGames = node.getComponent(TrafficGames)

            if (this.trafficGames) {
                // 将节点添加到指定容器中
                this.trafficGames.addToParent(this.trafficGamesContainer)
                this.scheduleOnce(() => {
                    this.trafficGames?.show()
                }, 0.1)
            } else {
                console.error('TrafficGamesEntry: TrafficGames组件获取失败')
            }
        } catch (error) {
            console.error('TrafficGamesEntry: 创建TrafficGames失败', error)
        }
    }

    override onDestroy(): void {}
}
