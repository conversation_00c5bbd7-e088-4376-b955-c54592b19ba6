/**
 * @describe 导量游戏主组件
 * <AUTHOR>
 * @date 2025-09-19
 *
 * 显示导量游戏列表，支持点击收起
 */

import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    _decorator,
    Node,
    Vec2,
    EventTouch,
    input,
    Input,
    UITransform,
    tween,
    Vec3,
    UIOpacity,
    director,
    Canvas,
} from 'cc'
import store from '@/core/business/store'
import { cat } from '@/core/manager'
import { RoomEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

export type TrafficGamesProps = {}

export type TrafficGamesData = {
    gameItems?: Array<{
        name: string
        icon: string
        url: string
    }>
}

@ccclass('TrafficGames')
export class TrafficGames extends BaseComponent<
    TrafficGamesProps,
    TrafficGamesData
> {
    /** 是否正在显示 */
    private _isShowing: boolean = false

    protected override onDestroy(): void {
        this.removeTouchListener()
    }

    protected override addListener(): void {
        cat.event.on(RoomEventConstant.HIDE_TRAFFIC_GAME, this.hide, this)
    }

    protected override removeListener(): void {
        cat.event.off(RoomEventConstant.HIDE_TRAFFIC_GAME, this.hide, this)
    }
    protected override initUI(): void {
        // 初始化状态
        this.node.setScale(new Vec3(0.8, 0.8, 1))
        const uiOpacity =
            this.node.getComponent(UIOpacity) ||
            this.node.addComponent(UIOpacity)
        uiOpacity.opacity = 0

        this.node.active = false
    }

    /**
     * 显示组件
     */
    show(): void {
        if (this._isShowing) return

        this._isShowing = true
        this.node.active = true
        this.addTouchListener()
        cat.event.dispatchEvent(RoomEventConstant.HIDE_POINT_POPUP)

        console.log(
            `[TrafficGames-${Date.now()}] 显示组件，_isShowing=${
                this._isShowing
            }, 添加触摸监听`
        )

        // 可以在这里添加显示动画
        this.playShowAnimation()
    }

    /**
     * 切换显示/隐藏状态
     */
    public toggle(): void {
        if (this._isShowing) {
            this.hide()
        } else {
            this.show()
        }
    }

    /**
     * 隐藏组件
     */
    public hide(): void {
        if (!this._isShowing) return

        this._isShowing = false
        this.removeTouchListener()

        console.log(
            `[TrafficGames-${Date.now()}] 隐藏组件，_isShowing=${
                this._isShowing
            }, 移除触摸监听`
        )

        // 可以在这里添加隐藏动画
        this.playHideAnimation(() => {
            this.node.active = false
            console.log(
                `[TrafficGames-${Date.now()}] 隐藏完成，node.active=${
                    this.node.active
                }`
            )
        })
    }

    /**
     * 添加触摸监听器（点击其他地方收起）
     */
    private addTouchListener(): void {
        console.log(`[TrafficGames-${Date.now()}] 准备添加root_ui触摸监听器`)

        // 尝试在cat.gui.root_ui上监听事件
        if (cat.gui && cat.gui.root_ui) {
            cat.gui.root_ui.on(
                Node.EventType.TOUCH_START,
                this.onTouchStart,
                this,
                true
            )
            console.log(
                `[TrafficGames-${Date.now()}] 在root_ui上添加触摸监听器成功`
            )
        }
    }

    /**
     * 移除触摸监听器
     */
    private removeTouchListener(): void {
        // 尝试从cat.gui.root_ui上移除监听事件
        if (cat.gui && cat.gui.root_ui) {
            cat.gui.root_ui.off(
                Node.EventType.TOUCH_START,
                this.onTouchStart,
                this,
                true
            )
            console.log(
                `[TrafficGames-${Date.now()}] 从root_ui上移除触摸监听器`
            )
        }
    }

    /**
     * 触摸开始事件
     */
    private onTouchStart(event: EventTouch): void {
        console.log(
            `[TrafficGames-${Date.now()}] 👋 触摸事件已触发！这是关键日志`
        )
        const touchLocation = event.getUILocation()
        console.log(
            `[TrafficGames-${Date.now()}] 触摸事件详细信息，坐标: (${
                touchLocation.x
            }, ${touchLocation.y})`
        )

        // 检查点击是否在组件范围内
        const isInComponent = this.isTouchInComponent(touchLocation)
        console.log(
            `[TrafficGames-${Date.now()}] 点击检测结果: isInComponent=${isInComponent}`
        )

        if (!isInComponent) {
            console.log(
                `[TrafficGames-${Date.now()}] 点击在外部区域，执行隐藏操作`
            )
            this.hide()
        } else {
            console.log(
                `[TrafficGames-${Date.now()}] 点击在组件内部，保持显示状态`
            )
        }
    }

    /**
     * 检查触摸点是否在组件范围内
     */
    private isTouchInComponent(worldPos: Vec2): boolean {
        if (!this.node || !this.node.uuid) {
            console.log(
                `[TrafficGames-${Date.now()}] 节点无效，node存在=${!!this
                    .node}, uuid存在=${!!this.node?.uuid}`
            )
            return false
        }

        // 获取组件在世界坐标系中的边界
        const uiTransform = this.node.getComponent(UITransform)
        if (!uiTransform) {
            console.log(`[TrafficGames-${Date.now()}] UITransform组件不存在`)
            return false
        }

        const bounds = uiTransform.getBoundingBoxToWorld()
        const contains = bounds.contains(worldPos)

        console.log(`[TrafficGames-${Date.now()}] 边界检测详情:`)
        console.log(`  - 触摸位置: (${worldPos.x}, ${worldPos.y})`)
        console.log(
            `  - 组件边界: x=${bounds.x}, y=${bounds.y}, width=${bounds.width}, height=${bounds.height}`
        )
        console.log(`  - 检测结果: ${contains ? '在内部' : '在外部'}`)

        return contains
    }

    /**
     * 显示动画
     */
    private playShowAnimation(): void {
        // 使用Cocos的tween系统
        const uiOpacity =
            this.node.getComponent(UIOpacity) ||
            this.node.addComponent(UIOpacity)

        tween(this.node)
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start()

        tween(uiOpacity).to(0.2, { opacity: 255 }).start()
    }

    /**
     * 隐藏动画
     */
    private playHideAnimation(callback?: () => void): void {
        // 使用Cocos的tween系统
        const uiOpacity = this.node.getComponent(UIOpacity)

        tween(this.node)
            .to(0.2, { scale: new Vec3(0.8, 0.8, 1) })
            .start()

        if (uiOpacity) {
            tween(uiOpacity)
                .to(0.2, { opacity: 0 })
                .call(() => {
                    if (callback) callback()
                })
                .start()
        } else {
            if (callback) callback()
        }
    }

    /**
     * 获取是否正在显示
     */
    public get isShowing(): boolean {
        return this._isShowing
    }
}
