/**
 * @describe 导量游戏列表组件
 * <AUTHOR>
 * @date 2025-09-19
 *
 * 负责 TrafficGameItem 的实例化和列表展示
 */

import {
    _decorator,
    Node,
    instantiate,
    Prefab,
    Label,
    Layout,
    Size,
    UITransform,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import store from '@/core/business/store'
import { TrafficGameItem } from './TrafficGameItem'

const { ccclass, property } = _decorator

export type TrafficGameListProps = {}

export type TrafficGameListData = {
    games?: Array<{
        name: string
        icon: string
        appId: string // 兼容现有数据结构
    }>
}

@ccclass('TrafficGameList')
export class TrafficGameList extends BaseComponent<
    TrafficGameListProps,
    TrafficGameListData
> {
    /** 游戏项预制体 */
    @property({ type: Prefab, tooltip: '游戏项预制体' })
    gameItemPrefab: Prefab = null!

    /** 列表容器节点 */
    @property({ type: Node, tooltip: '列表容器节点' })
    container: Node = null!

    protected override onLoad(): void {
        this.updateGameList()
    }

    protected override initUI(): void {}

    protected override onAutoObserver(): void {
        // 监听游戏列表数据变化
        this.addReaction(
            () => store.user.trafficGames,
            (gameList) => {
                // 类型转换：store中的数据可能只有appId，需要转换为url
                this.data.games = gameList?.map((game: any) => ({
                    name: game.name,
                    icon: game.icon,
                    appId: game.appId, // 保留原始数据
                }))
                this.updateGameList()
            },
            { fireImmediately: false }
        )
    }

    /**
     * 更新游戏列表
     */
    private updateGameList(): void {
        const games = this.data.games || store.user.trafficGames

        if (!games || games.length === 0) {
            this.node.active = false
            return
        }

        this.node.active = true

        if (!this.container || !this.gameItemPrefab) {
            console.error('[TrafficGameList] 缺少必要组件或预制体')
            return
        }

        // 清空现有子节点
        this.container.removeAllChildren()

        // 实例化游戏项
        games.forEach((gameData, index) => {
            this.createGameItem(gameData, index)
        })

        // 更新布局
        this.updateContainerSize(games.length)
    }

    /**
     * 创建单个游戏项
     */
    private createGameItem(
        gameData: { name: string; icon: string; appId: string },
        index: number
    ): void {
        try {
            const itemNode = instantiate(this.gameItemPrefab)
            const gameItem = itemNode.getComponent(TrafficGameItem)

            if (!gameItem) {
                console.error(
                    `[TrafficGameList] 第${index + 1}个游戏项组件获取失败`
                )
                itemNode.destroy()
                return
            }

            // 添加到父节点并设置数据
            gameItem.addToParent(this.container, {
                props: { gameData },
            })

            console.log(`[TrafficGameList] 成功创建游戏项: ${gameData.name}`)
        } catch (error) {
            console.error(
                `[TrafficGameList] 创建游戏项失败 (${gameData.name}):`,
                error
            )
        }
    }

    /**
     * 更新容器大小
     */
    private updateContainerSize(itemCount: number): void {
        const uiTransform = this.node.getComponent(UITransform)
        if (!uiTransform) return

        // 获取 container 上的 Layout 组件
        const layout = this.container.getComponent(Layout)
        if (!layout) {
            console.warn(
                '[TrafficGameList] container 缺少 Layout 组件，使用默认值'
            )
            return
        }

        // 获取 Layout 组件的实际属性
        const padding = {
            left: layout.paddingLeft,
            right: layout.paddingRight,
            top: layout.paddingTop,
            bottom: layout.paddingBottom,
        }
        const spacing = layout.spacingX // 水平间距

        // 获取第一个子节点的实际宽度（TrafficGameItem）
        let itemWidth = 160 // 默认值
        if (this.container.children.length > 0) {
            const firstChild = this.container.children[0]
            const childTransform = firstChild.getComponent(UITransform)
            if (childTransform) {
                itemWidth = childTransform.contentSize.width
            }
        }

        // 计算容器总宽度
        const totalWidth =
            padding.left +
            padding.right +
            itemWidth * itemCount +
            spacing * (itemCount - 1)

        console.log(
            `[TrafficGameList] 布局计算: ${itemCount}个项目，每个宽度${itemWidth}，间距${spacing}，padding${JSON.stringify(
                padding
            )}`
        )

        uiTransform.contentSize = new Size(
            totalWidth,
            uiTransform.contentSize.height
        )
    }
}
