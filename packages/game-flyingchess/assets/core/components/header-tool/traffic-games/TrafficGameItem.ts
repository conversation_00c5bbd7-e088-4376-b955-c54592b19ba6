/**
 * @describe 导量游戏项组件
 * <AUTHOR>
 * @date 2025-09-19
 *
 * 展示单个导量游戏的图标和名字
 */

import {
    _decorator,
    Node,
    Sprite,
    Label,
    EventTouch,
    Input,
    SpriteFrame,
    ImageAsset,
    isValid,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'

const { ccclass, property } = _decorator

export type TrafficGameItemProps = {
    gameData: {
        name: string
        icon: string
        appId: string
    }
}

export type TrafficGameItemData = {}

@ccclass('TrafficGameItem')
export class TrafficGameItem extends BaseComponent<
    TrafficGameItemProps,
    TrafficGameItemData
> {
    /** 游戏图标节点 */
    @property({ type: Sprite, tooltip: '游戏图标节点' })
    icon_node: Sprite = null!

    /** 游戏名称节点 */
    @property({ type: Label, tooltip: '游戏名称节点' })
    name_node: Label = null!

    /** 按钮点击节点 */
    @property({ type: Node, tooltip: '按钮点击节点' })
    button_node: Node = null!

    protected override onLoad(): void {
        // 注册按钮点击事件
        if (this.button_node) {
            this.button_node.on(
                Input.EventType.TOUCH_END,
                this.onGameClick,
                this
            )
        }
    }

    protected override onDestroy(): void {
        // 移除事件监听
        if (this.button_node) {
            this.button_node.off(
                Input.EventType.TOUCH_END,
                this.onGameClick,
                this
            )
        }
    }

    protected override initUI(): void {
        const { gameData } = this.props

        // 设置游戏名称
        if (this.name_node) {
            this.name_node.string = gameData.name
        }

        // 加载游戏图标
        if (this.icon_node && gameData.icon) {
            this.loadGameIcon(gameData.icon)
        }
    }

    protected override onAutoObserver(): void {
        // 可以在这里添加响应式数据监听，如需要的话
    }

    /**
     * 加载游戏图标
     */
    private loadGameIcon(iconUrl: string): void {
        if (!iconUrl) {
            console.warn('[TrafficGameItem] 游戏图标URL为空')
            return
        }

        // 使用 cat.res 加载远程图片
        cat.res.loadRemote(iconUrl, (err: any, imageAsset: ImageAsset) => {
            if (err) {
                console.error(`[TrafficGameItem] 加载游戏图标失败: ${err}`)
                return
            }

            if (!imageAsset || !isValid(this.icon_node)) {
                console.warn('[TrafficGameItem] 图标节点无效或图片资源为空')
                return
            }

            try {
                // 创建 SpriteFrame
                const spriteFrame = SpriteFrame.createWithImage(imageAsset)
                if (spriteFrame) {
                    this.icon_node.spriteFrame = spriteFrame
                }
            } catch (error) {
                console.error(
                    `[TrafficGameItem] 创建 SpriteFrame 失败: ${error}`
                )
            }
        })
    }

    /**
     * 游戏点击事件处理
     */
    private onGameClick(event: EventTouch): void {
        console.log(`[TrafficGameItem] 点击游戏: ${this.props.gameData.name}`)

        // 处理游戏跳转逻辑
        this.handleGameJump()
    }

    /**
     * 处理游戏跳转
     */
    private handleGameJump(): void {
        const { gameData } = this.props

        try {
            wx.navigateToMiniProgram({
                appId: gameData.appId,
                success(res) {
                    // 打开成功
                    console.log('成功打开小游戏')
                },
            })
        } catch (error) {
            console.error(`[TrafficGameItem] 跳转小游戏失败: ${error}`)
        }
    }

    /**
     * 获取游戏数据
     */
    public getGameData() {
        return this.props.gameData
    }
}
