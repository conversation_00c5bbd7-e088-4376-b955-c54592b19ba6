import { audioEffect } from '@/core/business/hooks/Decorator'
import { cat } from '@/core/manager'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { _decorator, Button, Component, instantiate, Node, Prefab } from 'cc'
import { UIRule } from '../../ui/UIRule'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

@ccclass('BtnRule')
export class BtnRule extends BaseComponent {
    @property({ type: Button, tooltip: '规则按钮' })
    btn_rule: Button

    protected override onLoad(): void {}

    override onDestroy(): void {}

    protected override initUI(): void {
        this.btn_rule.node.on(Button.EventType.CLICK, this.onRuleHander, this)
        cat.event.on(
            GameEventConstant.SHOW_RULE_DIALOG,
            this.onRuleHander,
            this
        )
    }

    @audioEffect()
    private onRuleHander() {
        const rulePrefab = cat.res.get<Prefab>('prefabs/components/rule/rule')
        if (!rulePrefab) {
            console.error('规则UI预制体加载失败：prefabs/components/rule/rule')
            return
        }

        const node = instantiate(rulePrefab)
        cat.gui.openUI<UIRule>(node)

        cat.trackManager.trackEvent({
            category: '游戏页面',
            label: '玩法介绍',
            value: JSON.stringify({
                game_state: store.game.roomData.stateInfo?.state,
            }),
        })
    }
}
