/**
 * @describe 微信平台游戏道具相关API
 * <AUTHOR>
 * @date 2024-12-20 15:30:00
 */

import { BaseGamePropsAPI } from './index'
import store from '../../store'
import { Manager } from '@/core/manager'

export class WXGamePropAPI extends BaseGamePropsAPI {
    constructor(cat: Manager) {
        super(cat)
    }

    /**
     * 获取限制性游戏道具配置
     * @param event 道具event
     * @returns 道具配置列表
     */
    getPropsConfig = (event: string) => {
        console.log('WXGamePropAPI-->getPropsConfig', event)
        return this.cat.api.user.get_game_limit_event({
            accessToken: store.login.access_token,
            serialNo: store.user.auth.battleId,
            boutId: store.user.auth.battleId,
            event: event,
        })
    }

    /**
     * 使用限制性游戏道具
     * @param event 道具event
     * @returns 道具使用结果
     */
    useProps = (event: string) => {
        console.log('WXGamePropAPI-->useProps', event)
        return this.cat.api.user.use_game_limit_event({
            accessToken: store.login.access_token,
            serialNo: store.user.auth.battleId,
            boutId: store.user.auth.battleId,
            event: event,
        })
    }

    /**
     * 获取非限制性道具列表
     * @param params 请求参数，支持 CScene 和 serialNo
     * @returns 非限制性道具列表
     */
    getGameEvents = (params: { CScene?: string; serialNo?: string } = {}) => {
        console.log('WXGamePropAPI-->getGameEvents', event)
        return this.cat.api.user.get_game_event({
            accessToken: store.login.access_token,
            serialNo: store.user.auth.battleId,
        })
    }

    /**
     * 使用非限制性道具
     * @param params 请求参数，支持 CScene、event、printUid 和 serialNo
     * @returns 使用道具结果
     */
    useGameEvent = (
        params: {
            CScene?: string
            event?: string
            printUid?: number
            serialNo?: string
        } = {}
    ) => {
        console.log('WXGamePropAPI-->useGameEvent', event)
        return this.cat.api.user.get_game_event({
            accessToken: store.login.access_token,
            serialNo: store.user.auth.battleId,
            event: params.event,
            printUid: params.printUid,
        })
    }

    /**
     * 打开游戏内购买页面
     * @param params 购买页面参数，支持默认值覆盖
     * @returns 桥接调用结果
     */
    openGamePropsBuy = (
        params: { serialNo?: string; useUid?: string } = {}
    ) => {
        console.log(
            'WXGamePropAPI-->openGamePropsBuy do not execute, return',
            params
        )
        return Promise.resolve(null)
    }
}
