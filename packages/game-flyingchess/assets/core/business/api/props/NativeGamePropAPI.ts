/**
 * @describe 原生平台游戏道具相关API
 * <AUTHOR>
 * @date 2024-12-20 15:30:00
 */

import { Bridge } from '../../bridge/Bridge'
import { BaseGamePropsAPI } from './index'
import { JSBridgeClient } from '../../jsbridge/JSBridge'
import store from '../../store'
import { Manager } from '@/core/manager'

export class NativeGamePropAPI extends BaseGamePropsAPI {
    constructor(cat: Manager) {
        super(cat)
    }

    /**
     * 获取游戏道具配置
     * @param event 道具event
     * @returns 道具配置列表
     */
    getPropsConfig = (event: string) => {
        console.log('NativeGamePropAPI-->getPropsConfig', event)
        return JSBridgeClient.callPlatformApi<IBridge.LimitPropConfigRes>({
            url: Bridge.EventName.APIName.PROP_CONFIG,

            params: {
                serialNo: store.user.auth.battleId,
                event,
            },
        })
    }

    /**
     * 使用游戏道具
     * @param event 道具event
     * @returns 道具使用结果
     */
    useProps = (event: string) => {
        return JSBridgeClient.callPlatformApi<IBridge.LimitPropUseRes>({
            url: Bridge.EventName.APIName.PROP_USE,
            params: {
                serialNo: store.user.auth.battleId,
                event,
            },
        })
    }

    /**
     * 获取非限制性道具列表
     * @param params 请求参数，支持 CScene 和 serialNo
     * @returns 非限制性道具列表
     */
    getGameEvents = (params: { CScene?: string; serialNo?: string } = {}) => {
        const defaultParams = {
            serialNo: store.user.auth.battleId,
        }
        return JSBridgeClient.callPlatformApi<IBridge.GameEventsRes>({
            url: Bridge.EventName.APIName.PROP_GAME_EVENTS,
            params: { ...defaultParams, ...params },
        })
    }

    /**
     * 使用非限制性道具
     * @param params 请求参数，支持 CScene、event、printUid 和 serialNo
     * @returns 使用道具结果
     */
    useGameEvent = (
        params: {
            CScene?: string
            event?: string
            printUid?: number
            serialNo?: string
        } = {}
    ) => {
        const defaultParams = {
            serialNo: store.user.auth.battleId,
        }
        return JSBridgeClient.callPlatformApi<IBridge.UseGameEventRes>({
            url: Bridge.EventName.APIName.PROP_USE_GAME_EVENT,
            params: { ...defaultParams, ...params },
        })
    }

    /**
     * 打开游戏内购买页面
     * @param params 购买页面参数，支持默认值覆盖
     * @returns 桥接调用结果
     */
    openGamePropsBuy = (
        params: { serialNo?: string; useUid?: string } = {}
    ) => {
        return JSBridgeClient.openGamePropsBuy(params)
    }
}
