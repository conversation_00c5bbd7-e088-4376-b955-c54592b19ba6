/**
 * @describe 游戏道具相关API
 * <AUTHOR>
 * @date 2024-12-20 15:30:00
 */

import { BaseAPI } from '../BaseAPI'

/**
 * 道具事件枚举
 */
export enum PropsEvent {
    /**换手牌 */
    CHANGE_CARD = 'ChangeCard',
    /**加时卡 */
    DURATION_CARD = 'DurationCard',
    /**生成谜面 */
    GEN_STORY = 'GenStory',

    // ## 飞行棋棋盘道具
    /**龙卷风 */
    TORNADO = 'Tornado',
    /**补给站 */
    POWER_STATION = 'PowerStation',

    // ## 飞行棋背包道具
    // | 道具名称 | 类型(event)    | 附加参数(params) |
    // |------|--------------|--------------|
    // | 遥控骰子 | ControlDice  | -            |
    // | 氮气加速 | NitrogenAcc  | -            |
    // | 冰冻 | Freeze       | -            |
    // | 防护罩 | ProtCover    | -            |
    // | 时空转换 | PositionSwap | -            |
    /**遥控骰子 */
    CONTROL_DICE = 'ControlDice',
    /**氮气加速 */
    NITROGEN_ACC = 'NitrogenAcc',
    /**冰冻 */
    FREEZE = 'Freeze',
    /**防护罩 */
    PROT_COVER = 'ProtCover',
    /**时空转换 */
    POSITION_SWAP = 'PositionSwap',
}

/**
 * 道具状态枚举
 */
export enum PropStatus {
    /**免费 */
    FREE = 1,
    /**付费 */
    PAY = 2,
    /**次数已用完 */
    USED_UP = 3,
}

/**
 * 抽象游戏道具API类
 */
export abstract class BaseGamePropsAPI extends BaseAPI {
    /**
     * 获取游戏道具配置
     * @param event 道具event
     * @returns 道具配置列表
     */
    abstract getPropsConfig(event: string): Promise<any>

    /**
     * 使用游戏道具
     * @param event 道具event
     * @returns 道具使用结果
     */
    abstract useProps(event: string): Promise<any>

    /**
     * 获取非限制性道具列表
     * @param params 请求参数，支持 CScene 和 serialNo
     * @returns 非限制性道具列表
     */
    abstract getGameEvents(params?: {
        CScene?: string
        serialNo?: string
    }): Promise<any>

    /**
     * 获取非限制性道具列表 (Mock 数据)
     * @returns Mock 的非限制性道具列表
     */
    getGameEventsMock(): Promise<IBridge.GameEventsRes> {
        const mockData: IBridge.GameEventsData = {
            props: [
                {
                    event: 'ControlDice',
                    eventName: '遥控骰子',
                    point: 100,
                    pointD10: Math.floor(Math.random() * 200) + 1,
                },
                {
                    event: 'NitrogenAcc',
                    eventName: '氮气加速',
                    point: 50,
                    pointD10: Math.floor(Math.random() * 200) + 1,
                },
                {
                    event: 'Freeze',
                    eventName: '冰冻',
                    point: 20,
                    pointD10: Math.floor(Math.random() * 200) + 1,
                },
                {
                    event: 'ProtCover',
                    eventName: '防护罩',
                    point: 180,
                    pointD10: Math.floor(Math.random() * 200) + 1,
                },
                {
                    event: 'PositionSwap',
                    eventName: '时空转换',
                    point: 2,
                    pointD10: Math.floor(Math.random() * 200) + 1,
                },
            ],
            userPoint: 1000,
            userPointD10: 50,
        }

        const mockResponse: IBridge.GameEventsRes = {
            code: 0,
            message: 'success',
            data: mockData,
        }

        return Promise.resolve(mockResponse)
    }

    /**
     * 使用非限制性道具
     * @param params 请求参数，支持 CScene、event、printUid 和 serialNo
     * @returns 使用道具结果
     */
    abstract useGameEvent(params?: {
        CScene?: string
        event?: string
        printUid?: number
        serialNo?: string
    }): Promise<any>

    /**
     * 打开游戏内购买页面
     * @param params 购买页面参数，支持默认值覆盖
     * @returns 桥接调用结果
     */
    abstract openGamePropsBuy(params?: {
        serialNo?: string
        useUid?: string
    }): Promise<any>
}