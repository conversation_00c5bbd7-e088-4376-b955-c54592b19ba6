import store from '../../store'
import { BaseAPI } from '../BaseAPI'

export class UserAPI extends BaseAPI {
    /**获取用户信息 */
    get_user_info = async () => {
        let data = { accessToken: store.login.access_token }
        const res = await this.api<UserInfo>({
            url: `yoo/wx/game/${this.gameCode}/info`,
            method: 'POST',
            data,
        })
        store.user.user_info = res
        console.log('UserAPI-->get_user_info():', res)
    }

    /**获取用户信息 - mock方法 */
    mock_get_user_info = async () => {
        const mockData = {
            userId: 12345,
            nickName: '测试用户',
            avatar: 'https://res-cdn.suileyoo.com/default-avatar.jpg',
            gender: 1 as 0 | 1 | 2,
            detailItems: [
                {
                    name: '场次',
                    value: '100',
                    unit: '',
                },
                {
                    name: 'MVP',
                    value: '25',
                    unit: '',
                },
                {
                    name: '胜率',
                    value: '68.5',
                    unit: '%',
                },
            ],
            gameLevel: '黄金',
            tierLevel: '黄金III',
            roomSerial: 'ROOM123456',
            pointD10: 8888,
        }
        store.user.user_info = mockData
        console.log('UserAPI-->mock_get_user_info() 返回mock数据:', mockData)
        return mockData
    }

    /**获取版本信息 */
    get_version_swich = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/ver`,
            method: 'POST',
            data,
        })
        store.user.versionStatus = res.status
    }

    /**获取微信导量游戏列表 */
    get_traffic_games = async () => {
        let data = { accessToken: store.login.access_token }
        const res = await this.api<any[]>({
            url: `yoo/wx/game/${this.gameCode}/trafficGames`,
            method: 'POST',
            data,
        })
        store.user.trafficGames = res
        console.log('UserAPI-->get_traffic_games():', res)
    }

    /**获取微信导量游戏列表 - mock方法 */
    mock_get_traffic_games = async () => {
        const mockData = [
            {
                name: '大表哥',
                icon: 'https://res-cdn.suileyoo.com/202012231018309715f48193d2c0e789bf9df047bc9003.jpg?width=300&height=300',
                appId: 'test-app-1',
            },
            {
                name: '艾尔登法环',
                icon: 'https://res-cdn.suileyoo.com/game/1804035199703625729.jpg?width=200&height=200',
                appId: 'test-app-2',
            },
            {
                name: '大表哥',
                icon: 'https://res-cdn.suileyoo.com/202012231018309715f48193d2c0e789bf9df047bc9003.jpg?width=300&height=300',
                appId: 'test-app-1',
            },
            {
                name: '艾尔登法环',
                icon: 'https://res-cdn.suileyoo.com/game/1804035199703625729.jpg?width=200&height=200',
                appId: 'test-app-2',
            },
            {
                name: '大表哥',
                icon: 'https://res-cdn.suileyoo.com/202012231018309715f48193d2c0e789bf9df047bc9003.jpg?width=300&height=300',
                appId: 'test-app-1',
            },
        ]
        store.user.trafficGames = mockData
        console.log(
            'UserAPI-->mock_get_traffic_games() 返回mock数据:',
            mockData
        )
        return mockData
    }

    // 获取用户限制性道具
    get_game_limit_event = async (data: any) => {
        const res = await this.api<any[]>({
            url: `yoo/wx/game/${this.gameCode}/gameLimitEvent`,
            method: 'POST',
            data,
        })

        console.log('UserAPI-->get_game_limit_event():', res)
        return res
    }

    // 使用用户限制性道具
    use_game_limit_event = async (data: any) => {
        const res = await this.api<any[]>({
            url: `yoo/wx/game/${this.gameCode}/useGameLimitEvent`,
            method: 'POST',
            data,
        })

        console.log('UserAPI-->use_game_limit_event():', res)
        return res
    }

    // 获取用户非限制性道具
    get_game_event = async (data: any) => {
        const res = await this.api<any[]>({
            url: `yoo/wx/game/${this.gameCode}/gameEvents`,
            method: 'POST',
            data,
        })

        console.log('UserAPI-->get_game_event():', res)
        return res
    }

    // 使用用户非限制性道具
    use_game_event = async (data: any) => {
        const res = await this.api<any[]>({
            url: `yoo/wx/game/${this.gameCode}/useGameEvent`,
            method: 'POST',
            data,
        })

        console.log('UserAPI-->use_game_event():', res)
        return res
    }

    start_reward_ad = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/startAds`,
            method: 'POST',
            data,
        })
        console.log('UserAPI-->start_reward_ad():', res)
        return res
    }

    end_reward_ad = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/endAds`,
            method: 'POST',
            data,
        })
        console.log('UserAPI-->end_reward_ad():', res)
        return res
    }
}
