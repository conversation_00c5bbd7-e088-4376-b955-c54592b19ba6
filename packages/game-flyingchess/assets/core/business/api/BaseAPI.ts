/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import ky, { HTTPError, KyInstance, Options } from 'ky'
import { Manager } from '@/core/manager'
import { getCommonSign } from '../hooks/api-sign/CommonSign'
import { attachSign } from '../hooks/api-sign/SuileyooSign'
import { error, log } from 'cc'
import { WECHAT } from 'cc/env'
import store from '../store'
import { GameCodeConstant } from '../constant/GameCodeConstant.b'

interface IResponse<T> {
    code: number // 请求消息码
    message: string // 错误信息
    data: T // 输出数据
    success: boolean // 请求成功标识
}

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

interface RequestConfig {
    url: string
    method?: RequestMethod
    data?: any
    headers?: Record<string, string>
    timeout?: number
}

export class BaseAPI {
    protected cat: Manager
    protected baseURL: string

    protected base: KyInstance
    protected gameCode = GameCodeConstant.GAME_CODE

    constructor(cat: Manager) {
        this.cat = cat
        this.baseURL = cat.env.http_base_url

        //this.initBase()

        if (WECHAT) {
            // 统一请求日志拦截
            this.requestInterceptor = this.requestInterceptor.bind(this)
        }
    }

    /** 请求拦截处理 */
    private requestInterceptor(config: RequestConfig) {
        const { method = 'POST', url, data } = config
        // 签名处理
        const { sign, timestamp } = getCommonSign(data)

        return {
            ...config,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${store.login.access_token}`,
                'X-Signature': sign,
                'X-Timestamp': timestamp,
                ...config.headers,
            },
        }
    }

    private initBase() {
        this.base = ky.create({
            prefixUrl: this.cat.env.http_base_url,
            retry: {
                limit: 10,
                backoffLimit: 3000,
            },
            hooks: {
                beforeRequest: [
                    async (request) => {
                        const body = await request.json()
                        const new_body = attachSign(body ?? {}) as Record<
                            string,
                            string
                        >
                        request.headers.set(
                            'Content-type',
                            'application/x-www-form-urlencoded'
                        )
                        window.ccLog(
                            `%c HTTP请求地址 %c ${request.method} %c ${request.url}`,
                            'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                            'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                            'background:transparent'
                        )
                        return new Request(request, {
                            body: new URLSearchParams(new_body),
                        })
                    },
                ],
                afterResponse: [
                    async (_request, _options, response) => {
                        window.ccLog(_request, _options, response)
                        if (response.ok) {
                            const data =
                                (await response.json()) as IResponse<void>
                            if (data.code !== 0 && data?.message) {
                                this.cat.gui.showToast({
                                    title: `${data?.message}`,
                                })
                            }
                        }
                    },
                ],
                beforeError: [
                    (error) => {
                        const { response } = error
                        if (response && response.body) {
                            window.ccLog('err', response)
                            this.cat.gui.showToast({
                                title: `REQUEST ERROR:${response.status}${
                                    response.statusText ?? ''
                                }`,
                            })
                        }
                        return error
                    },
                ],
            },
        })
    }
    // async api<T>(url: string, options: Options) {
    //     // try {
    //     const res = (await this.base
    //         .extend(options)(url)
    //         .json()) as IResponse<T>
    //     return res.data
    //     // } catch (err) {
    //     //     if (err instanceof HTTPError) {
    //     //         if (err.name === 'HTTPError') {
    //     //             const errorJson = await err.response.json();
    //     //             error(errorJson)
    //     //             this.cat.gui.showToast({
    //     //                 title: `${errorJson.status}:${errorJson.error}${errorJson.statusText ?? ''}`,
    //     //             });
    //     //         }
    //     //     }
    //     // }
    // }

    /** 核心请求方法 */
    protected async api<T>(config: RequestConfig): Promise<T> {
        const processedConfig = this.requestInterceptor(config)

        // 平台分支
        if (WECHAT) {
            console.log('BaseAPI-->api():', config)
            return this.wechatRequest<T>(processedConfig)
        } else {
            const res = (await this.base
                .extend(config.data)(config.url)
                .json()) as IResponse<T>
            return res.data
        }
    }

    /** 微信小程序请求实现 */
    private wechatRequest<T>(config: RequestConfig): Promise<T> {
        return new Promise((resolve, reject) => {
            wx.request({
                url: this.baseURL + '/' + config.url,
                method: config.method,
                data: config.data,
                header: config.headers,
                timeout: config.timeout || 5000,
                success: (res) => {
                    const response = res.data as IResponse<T>
                    if (res.statusCode !== 200) {
                        this.cat.gui.showToast({ title: res.errMsg })
                        reject(res.errMsg)
                        return
                    }
                    if (response.code === 0) {
                        resolve(response.data)
                    } else {
                        this.cat.gui.showToast({ title: response.message })
                        reject(response)
                    }
                },
                fail: (err) => {
                    log('err', err)
                    this.cat.gui.showToast({ title: err.errMsg })
                    reject({
                        code: -1,
                        message: err.errMsg,
                        data: null,
                        success: false,
                    })
                },
            })
        })
    }
}
