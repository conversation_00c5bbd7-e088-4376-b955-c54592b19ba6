import { log } from 'cc'

import store from '../../store'
import { BaseAPI } from '../BaseAPI'
import { TrackManager } from '@/core/manager/track'

export class LoginAPI extends BaseAPI {
    /**获取平台授权token */
    login = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/auth`,
            method: 'POST',
            data: data,
        })
        store.login.wx_openid = res.openid
        store.login.tempToken = res.tempToken
        this.cat.trackManager.putCache('wx_openid', res.openid)
        console.log('获取平台授权token====', store.login.tempToken)
    }

    //平台登录
    appLogin = async () => {
        const { rawData, signature, encryptedData, iv, cloudID } =
            store.user.wx_info
        let data = {
            rawData,
            signature,
            encryptedData,
            cloudID,
            iv,
            tempToken: store.login.tempToken,
        }
        window.ccLog('LoginAPI-->appLogin, data:', data)
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/login`,
            method: 'POST',
            data: data,
        })
        store.login.access_token = res.accessToken
    }
}
