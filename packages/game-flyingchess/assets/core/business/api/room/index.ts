/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import store from '../../store'
import { BaseAPI } from '../BaseAPI'

export class RoomAPI extends BaseAPI {
    /**匹配房间 */
    match = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/match`,
            method: 'POST',
            data,
        })
        store.room.roomSerial = res.roomSerial
        console.log('RoomAPI-->match(), result', res)
    }

    /**匹配好友 */
    match_friend = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/friendMatch`,
            method: 'POST',
            data,
        })
        store.room.roomSerial = res.roomSerial
        console.log('RoomAPI-->match_friend(), result', res)
    }

    /**加入房间 */
    join_friend = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/enter`,
            method: 'POST',
            data,
        })
        store.room.chatInfo = res.chatInfo
        console.log('RoomAPI-->join_friend(), result', res)
    }

    /**房间信息 */
    get_room_info = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/info`,
            method: 'POST',
            data,
        })
        store.room.roomInfo = res
    }

    /**准备 */
    ready = async (data: any) => {
        await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/ready`,
            method: 'POST',
            data,
        }).then(
            () => {},
            (res) => {
                if (res.code == 132215) {
                    store.lobby.guideShow = true
                }
            }
        )
    }

    /**取消准备 */
    unReady = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/unReady`,
            method: 'POST',
            data,
        })
    }

    /**开始游戏 */
    start_game = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/start`,
            method: 'POST',
            data,
        })
    }

    /**进入游戏 */
    enter_game = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/enter`,
            method: 'POST',
            data,
        })
        store.room.gameConfig = res
    }

    /**退出房间 */
    quit_room = async (data: any) => {
        await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/quit`,
            method: 'POST',
            data,
        }).then(
            () => {
                store.room.roomInfo = {}
                store.room.roomSerial = ''
                store.user.user_info.roomSerial = ''
            },
            () => {
                console.log('退出房间失败')
            }
        )
    }

    /**换座 */
    mikeChange = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/room/mikeChange`,
            method: 'POST',
            data,
        })
    }

    /**游戏结算 */
    settleInfo = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/settleInfo`,
            method: 'POST',
            data,
        })
        store.room.rankInfo = res
        console.log('RoomAPI-->settleInfo(), result', res)
    }

    gameTips = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/gameTips`,
            method: 'POST',
            data,
        })
        store.room.gameTips = res
        console.log('RoomAPI-->gameTips(), result', res)
    }

    //查询房间内用户昵称头像
    userInfos = async (data: any) => {
        const res = await this.api<any>({
            url: `yoo/wx/game/${this.gameCode}/userInfos`,
            method: 'POST',
            data,
        })
        store.room.addUserInfo(res)
        console.log('RoomAPI-->userInfos(), result', res)
    }
}
