/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { log } from 'cc'
import { Manager } from '@/core/manager'
import { LoginAPI } from './login'
import { SuileyooAPI } from './suileyoo'
import { UserAPI } from './user'
import { BaseGamePropsAPI } from './props'
import { NativeGamePropAPI } from './props/NativeGamePropAPI'
import { WXGamePropAPI } from './props/WXGamePropAPI'
import 'core-js/es/object/index.js'
import { RoomAPI } from './room'
import { WECHAT } from 'cc/env'

declare module '@/core/manager' {
    interface Manager {
        api: API
    }
}

export class API {
    login: LoginAPI
    user: UserAPI
    suileyoo: SuileyooAPI
    props: BaseGamePropsAPI
    room: RoomAPI

    constructor(cat: Manager) {
        window.ccLog('api cos')
        this.login = new LoginAPI(cat)
        window.ccLog('api login')
        this.user = new UserAPI(cat)
        window.ccLog('api user')
        this.suileyoo = new SuileyooAPI(cat)
        window.ccLog('api suileyoo')
        // 根据平台选择合适的GamePropsAPI实现
        if (WECHAT) {
            this.props = new WXGamePropAPI(cat)
        } else {
            this.props = new NativeGamePropAPI(cat)
        }
        window.ccLog('api props')
        this.room = new RoomAPI(cat)
        window.ccLog('api room')
    }
}
