/**
 * @describe 飞行棋游戏音效常量
 * @date 2025-6-30
 */

export enum AudioEffectConstant {
    /**背景音乐 */
    BGM = 'audio/flyingchess_bgm',
    /**游戏开始 */
    GAME_START = 'audio/flyingchess_startgame',
    /**回合开始 */
    ROUND_START = 'audio/flyingchess_roundstart',
    /**掷骰子 */
    ROLLING_DICE = 'audio/flyingchess_rollingdice',
    /**再掷一次骰子 */
    ONE_MORE_DICE = 'audio/flyingchess_onemoredice',
    /**棋子选中 */
    CHESS_SELECTED = 'audio/flyingchess_chessselected',
    /**棋子移动 */
    CHESS_MOVE = 'audio/flyingchess_chessmove',
    /**棋子跳跃 */
    CHESS_JUMP = 'audio/flyingchess_chessjump',
    /**棋子飞行 */
    CHESS_FLY = 'audio/flyingchess_chessfly',
    /**起飞/发射 */
    LAUNCH = 'audio/flyingchess_launch',
    /**撞击/碰撞 */
    CRASH = 'audio/flyingchess_crash',
    /**转向 */
    TURN_AROUND = 'audio/flyingchess_turnaround',
    /**到达终点 */
    FINISHING_POINT = 'audio/flyingchess_finishingpoint',
    /**胜利 */
    VICTORY = 'audio/flyingchess_victory',
    /**失败 */
    DEFEAT = 'audio/flyingchess_defeat',
    /**点击背包 */
    CLICK_BAG = 'audio/flyingchess_clickbag',
    /**倒计时 */
    COUNTDOWN = 'audio/flyingchess_countdown',
    /**事件 */
    EVENTS = 'audio/flyingchess_events',
    /**冻结 */
    FREEZE = 'audio/flyingchess_freeze',
    /**棋子被冻结 */
    FROZEN_CHESS = 'audio/flyingchess_frozenchess',
    /**氮气加速 */
    NITROGEN_ACCELERATION = 'audio/flyingchess_nitrogenacceleration',
    /**保护罩 */
    PROTECTIVE_COVER = 'audio/flyingchess_protectivecover',
    /**购买道具 */
    PURCHASE_PROPS = 'audio/flyingchess_purchaseprops',
    /**遥控器 */
    REMOTE_CONTROL = 'audio/flyingchess_remotecontrol',
    /**选择道具 */
    SELECT_PROPS = 'audio/flyingchess_selectprops',
    /**道具展示 */
    SUPPLEMENTARY_PROPS = 'audio/flyingchess_supplementaryprops',
    /**补给 */
    SUPPLY = 'audio/flyingchess_supply',
    /**龙卷风 */
    TORNADO = 'audio/flyingchess_tornado',
    /**静音 */
    SILENT = 'audio/silent',
    /**点击 */
    CLICK = 'audio/click',
}
