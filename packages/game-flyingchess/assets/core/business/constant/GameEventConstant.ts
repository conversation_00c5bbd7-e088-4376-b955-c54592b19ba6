import { GameEventConstant as BusinessGameEventConstant } from './GameEventConstant.b'

enum BaseGameEventConstant {
    /**关闭通用UI */
    CLOSE_COMMON_UI = 'GameEventConstant/CLOSE_COMMON_UI',

    /*显示通用弹窗UI */
    SHOW_COMMON_UI = 'GameEventConstant/SHOW_COMMON_UI',

    /**云信踢出房间 */
    KICK_ROOM_INFO = 'GameEventConstant/KICK_ROOM_INFO',
    /**云信更新房间信息 */
    UPDATE_ROOM_INFO = 'GameEventConstant/UPDATE_ROOM_INFO',
    /**云信游戏结算通知 */
    GAME_SETTLE_NOTIFY = 'GameEventConstant/GAME_SETTLE_NOTIFY',
    /**云信游戏小结算通知 */
    GAME_ME_OVER = 'GameEventConstant/GAME_ME_OVER',

    /**关闭小结算弹窗 */
    CLOSE_SMALL_SETTLE = 'GameEventConstant/CLOSE_SMALL_SETTLE',
    /**第一次进lobby时自动打开玩法弹窗 */
    SHOW_RULE_DIALOG = 'GameEventConstant/SHOW_RULE_DIALOG',
}

export const GameEventConstant = {
    ...BusinessGameEventConstant,
    ...BaseGameEventConstant,
}
