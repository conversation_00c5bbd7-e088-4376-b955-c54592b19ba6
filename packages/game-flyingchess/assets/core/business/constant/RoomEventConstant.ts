/**
 * @describe 房间事件监听方法
 * <AUTHOR>
 * @date 2023-08-03 18:13:36
 */

export enum RoomEventConstant {
    /** 切换麦位 */
    MIKE_CHANGE = 'RoomEventConstant/MIKE_CHANGE',
    /** 更新麦克风状态 */
    UPDATE_MIC_STATE = 'RoomEventConstant/UPDATE_MIC_STATE',
    /** 更新麦位控制器 */
    UPDATE_MIKE_CONTROLLER = 'RoomEventConstant/UPDATE_MIKE_CONTROLLER',
    /**赛季跳转 */
    JUMP_SEASON = 'RoomEventConstant/JUMP_SEASON',
    /**更新房间列表 */
    UDPATE_ROOM_LIST = 'RoomEventConstant/UDPATE_ROOM_LIST',

    UPDATE_PLATFORM = 'GameEventConstant/UPDATE_PLATFORM',

    //收到新的房间消息
    ROOM_MESSAGE_INCOMING = 'RoomEventConstant/ROOM_MESSAGE_INCOMING',

    /**隐藏键盘 */
    HIDE_KEYBOARD = 'RoomEventConstant/HIDE_KEYBOARD',

    /**隐藏激励视频引导浮窗 */
    HIDE_POINT_POPUP = 'RoomEventConstant/HIDE_POINT_POPUP',

    /**隐藏小游戏导量浮窗 */
    HIDE_TRAFFIC_GAME = 'RoomEventConstant/HIDE_TRAFFIC_GAME',
}
