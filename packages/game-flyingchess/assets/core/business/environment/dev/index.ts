/**
 * @describe 开发环境配置
 * <AUTHOR>
 * @date 2023-08-02 19:46:13
 */

import { WECHAT } from 'cc/env'
import { queryStringToObject } from '../util'
const queryParams = queryStringToObject(
    location.search ? location.search.slice(1) : ''
)

// ws://192.168.33.33:6551/ws/conn
// http://192.168.33.33:6550
const ENV_TYPE: RuntineEnv = {
    static_base_url: '',
    http_base_url: WECHAT
        ? 'https://igame-dev.suileyoo.com/proxy-api-local-mityoo'
        : 'https://dev-cloud-game-api.stnts.com/',
    event_tracking_url: 'https://dssp-test.stnts.com/?opt=put&type=json',
    event_tracking_key: 'alibabatutudodo@',
    td_debug_mode: 'debugOnly', // 只对数据做校验，不会入库；不建议在线上环境使用
    bucket: '',
    region: '',
    secret: '5bozaf6by&w3^4i$mll1uj0yzyfhx8t_',
    // player_ws_url: 'wss://gam-dev.yiqiyoo.com/ws/conn',
    // audience_ws_url: 'wss://gam-dev.yiqiyoo.com/ws/aud',
    player_ws_url:
        queryParams.player_ws_url ||
        'wss://game-server-dev.mityoo.com/ws/connector',
    audience_ws_url:
        queryParams.audience_ws_url ||
        'wss://game-server-dev.mityoo.com/ws/audience',
    stop_service_url: '',
    wechat_user_image_url: 'https://hz.s3down.com/test-mityoo-25446780999/test',
}
export default ENV_TYPE
