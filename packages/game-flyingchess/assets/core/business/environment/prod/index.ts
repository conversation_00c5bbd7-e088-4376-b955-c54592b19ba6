/**
 * @describe 生产环境配置
 * <AUTHOR>
 * @date 2023-08-02 19:46:13
 */

import { WECHAT } from 'cc/env'
import { queryStringToObject } from '../util'
const queryParams = queryStringToObject(
    location.search ? location.search.slice(1) : ''
)

const ENV_TYPE: RuntineEnv = {
    static_base_url: '',
    http_base_url: 'https://api.mityoo.com',
    event_tracking_url: 'https://dssp.stnts.com/?opt=put&type=json',
    event_tracking_key: 'alibabatutudodo@',
    td_debug_mode: 'none', // 数据会存入缓存，并依据一定的缓存策略上报,默认为NORMAL模式；建议在线上环境使用
    bucket: '',
    region: '',
    secret: '',
    player_ws_url:
        queryParams.player_ws_url || 'wss://game-ws.mityoo.com/ws/connector',
    audience_ws_url:
        queryParams.audience_ws_url || 'wss://game-ws.mityoo.com/ws/audience',
    stop_service_url: '',
    wechat_user_image_url: 'https://res-cdn.mityoo.com',
}
export default ENV_TYPE
