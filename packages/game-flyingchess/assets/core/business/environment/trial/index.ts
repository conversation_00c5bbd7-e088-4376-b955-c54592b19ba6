/**
 * @describe 测试环境配置
 * <AUTHOR>
 * @date 2023-08-02 19:46:13
 */
import { WECHAT } from 'cc/env'
import { queryStringToObject } from '../util'
const queryParams = queryStringToObject(
    location.search ? location.search.slice(1) : ''
)
const ENV_TYPE: RuntineEnv = {
    static_base_url: '',
    http_base_url: WECHAT
        ? 'https://igame-dev.suileyoo.com/proxy-api-testing-mityoo'
        : 'https://test-cloud-game-api.stnts.com/',
    event_tracking_url: 'https://dssp-test.stnts.com/?opt=put&type=json',
    event_tracking_key: 'alibabatutudodo@',
    td_debug_mode: 'debug', // 数据逐条上报。当出现问题时会以日志和异常的方式提示用户；不建议在线上环境使用
    bucket: '',
    region: '',
    secret: '',
    player_ws_url:
        queryParams.player_ws_url ||
        'wss://game-ws-testing.mityoo.com/ws/connector',
    audience_ws_url:
        queryParams.audience_ws_url ||
        'wss://game-ws-testing.mityoo.com/ws/audience',

    stop_service_url: '',
    wechat_user_image_url: 'https://hz.s3down.com/test-mityoo-25446780999/test',
}
export default ENV_TYPE
