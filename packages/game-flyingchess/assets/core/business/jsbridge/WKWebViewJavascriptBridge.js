/* eslint-disable */

;(function () {
  if (window.WKWebViewJavascriptBridge) {
    return
  }

  if (!window.onerror) {
    window.onerror = function (msg, url, line) {
      console.log(
        'WKWebViewJavascriptBridge: ERROR:' + msg + '@' + url + ':' + line
      )
    }
  }
  window.WKWebViewJavascriptBridge = {
    registerHandler: registerHandler,
    callHandler: callHandler,
    _fetchQueue: _fetchQueue,
    _handleMessageFromiOS: _handleMessageFromiOS,
  }

  var sendMessageQueue = []
  var messageHandlers = {}

  var responseCallbacks = {}
  var uniqueId = 1
  //web端注册一个消息方法
  function registerHandler(handlerName, handler) {
    messageHandlers[handlerName] = handler
  }
  //web端调用一个OC注册的消息
  function callHandler(handlerName, data, responseCallback) {
    if (arguments.length == 2 && typeof data == 'function') {
      responseCallback = data
      data = null
    }
    _doSend({ handlerName: handlerName, data: data }, responseCallback)
  }
  //把消息从JS发送到OC，执行具体的发送操作。
  function _doSend(message, responseCallback) {
    if (responseCallback) {
      var callbackID = 'cb_' + uniqueId++ + '_' + new Date().getTime()
      responseCallbacks[callbackID] = responseCallback
      message['callbackID'] = callbackID
    }
    sendMessageQueue.push(message)
    // messageHandlers浏览器下报undefined
    if (messageHandlers) {
      window.webkit
        ? window.webkit.messageHandlers.iOS_Native_FlushMessageQueue.postMessage(
            null
          )
        : ''
    }
  }
  //把消息转换成JSON字符串返回
  function _fetchQueue() {
    var messageQueueString = JSON.stringify(sendMessageQueue)
    sendMessageQueue = []
    return messageQueueString
  }
  //处理从OC返回的消息。
  function _dispatchMessageFromiOS(messageJSON) {
    console.log('_dispatchMessageFromiOS： ', messageJSON)
    var message = JSON.parse(messageJSON)
    console.log(message, responseCallbacks)
    var messageHandler
    var responseCallback
    if (message.responseID) {
      responseCallback = responseCallbacks[message.responseID]
      if (!responseCallback) {
        return
      }
      responseCallback(message.responseData)
      delete responseCallbacks[message.responseID]
    } else {
      if (message.callbackID) {
        var callbackResponseId = message.callbackID
        responseCallback = function (responseData) {
          _doSend({
            handlerName: message.handlerName,
            responseID: callbackResponseId,
            responseData: responseData,
          })
        }
      }

      var handler = messageHandlers[message.handlerName]
      if (!handler) {
        console.log(
          'WKWebViewJavascriptBridge: WARNING: no handler for message from iOS:',
          message
        )
      } else {
        handler(message.data, responseCallback)
      }
    }
  }
  //OC调用JS的入口方法
  function _handleMessageFromiOS(messageJSON) {
    _dispatchMessageFromiOS(messageJSON)
  }

  setTimeout(_callWVJBCallbacks, 0)
  //初始化WEB中注册的方法。这个方法会把WEB中的hander注册到bridge中。
  //下面的代码其实就是执行WEB中的callback函数。
  function _callWVJBCallbacks() {
    var callbacks = window.WVJBCallbacks || []
    delete window.WVJBCallbacks
    for (var i = 0; i < callbacks.length; i++) {
      callbacks[i](WKWebViewJavascriptBridge)
    }
  }
})()
/* eslint-disable */
