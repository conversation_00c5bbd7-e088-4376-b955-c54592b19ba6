/**
 * @describe 带带桥接
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { HTML5, WECHAT } from 'cc/env'
import { getURLParameters } from '../util/StringUtil'

import { log, game } from 'cc'
import { MessageManager } from '@/core/manager/event'

import dsBridge from 'dsbridge'
import { cat } from '@/core/manager'
import { AudioEffectConstant } from '../constant'
import store from '../store'
import './WKWebViewJavascriptBridge.js'

export enum PlatformType {
    ANDROID = 'android',
    IOS = 'ios',
    WEB = 'web',
    FLUTTER = 'flutter',
}

export enum GameCloseType {
    JoinOverTime, //加入游戏超时
    GameOver, //游戏结束
}

/**关闭游戏类型**/
export enum AudioState {
    ON = 0, //开
    OFF = 1, //关
}

const setupWKWebViewJavascriptBridge = (callback: (bridge: any) => void) => {
    const _win: any = window
    if (_win?.WKWebViewJavascriptBridge) {
        return callback(_win.WKWebViewJavascriptBridge)
    }
    if (_win?.WKWVJBCallbacks) {
        return _win.WKWVJBCallbacks.push(callback)
    }
    _win.WKWVJBCallbacks = [callback]
    try {
        _win.webkit.messageHandlers.iOS_Native_InjectJavascript.postMessage(
            null
        )
    } catch (e) {}
}

enum EnumJSBridgeClient {
    /** 关闭游戏 @param type 0-异常结束 1-正常结束 */
    CLOSE_GAME = 'close_game',
    /** 游戏加载完成 */
    GAME_LOAD_FINISHED = 'game_load_finished',
    /** 打开用户资料页 @param userId 用户ID */
    OPEN_PROFILE = 'open_profile',
    /** 获取音效状态 @returns 0-开启 1-关闭 */
    GET_AUDIO_STATE = 'get_audio_state',
    /** 禁用平台触摸事件 @param disable true-禁用 false-启用 */
    DISABLE_TOUCH = 'disable_touch',
    /**
     * 禁用聊天功能
     * @param disable true-禁用 false-允许
     * @default false
     */
    DISABLE_CHAT = 'disable_chat',
    /**
     * AI绘图开关
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ 5.6.1
     */
    AI_DRAW_SWITCH = 'ai_draw_switch',
    /**
     * AI绘图功能
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ 5.6.1
     */
    AI_DRAW = 'ai_draw',
    /**
     * 显示换词数据
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ 5.5.1
     */
    SHOW_CHANGE_WORD = 'show_change_word',
    /**
     * 执行换词操作
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ 5.5.2
     */
    CHANGE_WORD = 'change_word',
    /**
     * 调用平台API
     * @param url API地址 例如"/japi/yoo/partyGame/op/aiDrawSwitch"
     * @param params API参数
     */
    CALL_PLATFORM_API = 'call_platform_api',
    /** 获取麦位麦克风状态 @returns [{userId: 用户ID, status: 0-禁麦闭麦 1-开麦 2-闭麦}] */
    GET_MIKE_VOICE_STATUS = 'get_mike_voice_status',
    /**
     * 启用平台输入框
     * @param enable true-输入内容桥接到游戏 false-不桥接
     */
    ENABLE_INPUT_BOX = 'enable_input_box',
    /**
     * 获取麦位用户信息
     * @returns [{
     *   userId: 用户ID,
     *   status: 0-禁麦闭麦 1-开麦 2-闭麦,
     *   displayName: 显示名称,
     *   avatar: 头像地址,
     *   avatarFrame: 头像框,
     *   mtyMemberLevel: 会员等级,
     *   mikeSoundWave: 声波数据
     * }]
     */
    GET_MIKE_USERS_INFO = 'get_mike_users_info',
    /** 保存日志 @param content 日志内容 */
    SAVE_LOG = 'save_log',
    /** 播放音效（单次） @param path 音频路径 */
    PLAY_AUDIO = 'play_audio',
    /** 播放背景音乐（循环） @param path 音频路径 */
    PLAY_MUSIC = 'play_music',

    /**
     * 震动
     *  {"list": [{ "time": 200, "intensity": 2 }], "interval": 100}
     */
    PLAY_VIBRATE = 'play_haptic',

    /**
     * 打开游戏内购买页
     * @param params
     * {"serialNo":"","useUid":""}
     */
    OPEN_GAME_PROPS_BUY = 'openGamePropsBuy',
    /**
     * 分发AI助手事件
     * @param params
     * {"type": 1 | 2, "content": string, "battleId": string, "extraParameters": string}
     * type: 1-基础播报类型 2-LLM类型
     * content: 事件内容
     * battleId: 战斗ID
     * extraParameters: JSON字符串, e.g. {"event_type": "some_event"}
     */
    DISPATCH_ASSISTANT_EVENT = 'dispatch_assistant_event',
}

export enum EnumJSBridgeWebView {
    /** 游戏启动 @param params 启动参数 */
    GAME_START = 'game_start',
    /** 音效状态变更 @param state 0-开启 1-关闭 */
    CHANGE_AUDIO_STATE = 'change_audio_state',
    /** 播放麦位表情 @param playUserId 用户ID @param playUrl 表情URL */
    PLAYMIKEEMOJI = 'playMikeEmoji',
    /** 聊天文本 @param text 聊天内容 */
    CHAT_TEXT = 'chat_text',
    /** 麦位声波用户集合 @param users 有声波的用户ID列表 */
    MIKE_SOUND_WAVE = 'mike_sound_wave',
    /**
     * 麦克风状态更新
     * @param status [{
     *   userId: 用户ID,
     *   status: 0-禁麦闭麦 1-开麦 2-闭麦
     * }]
     */
    MIKE_VOICE_STATUS = 'mike_voice_status',
    /**
     * 麦位用户信息更新
     * @param users [{
     *   userId: 用户ID,
     *   status: 0-禁麦闭麦 1-开麦 2-闭麦,
     *   displayName: 显示名称,
     *   avatar: 头像地址,
     *   // ...其他字段同GET_MIKE_USERS_INFO
     * }]
     */
    MIKE_USERS_INFO = 'mike_users_info',
    /** 用户余额变更通知 */
    UPDATE_BALANCE = 'updateBalance',
    /** 游戏房间消息通知 @param msg 消息内容（具体格式与服务器约定） */
    USER_ROOM_GAME_MSG = 'user_room_game_msg',
}

const url_params = HTML5
    ? getURLParameters(decodeURIComponent(window.location.href))
    : {}
const platform: PlatformType =
    (url_params?.platform as PlatformType) || PlatformType.WEB

class JSBridgeClientAPI {
    private callHandler<T = void>(name: string, params: any = {}) {
        window.ccLog('callHandler', name, params)
        return new Promise<T>((resolve) => {
            switch (platform) {
                case PlatformType.ANDROID:
                    ;(window as any)?.WebViewJavascriptBridge?.callHandler(
                        name,
                        params,
                        (res: T) => {
                            // 处理安卓返回的数据
                            window.ccLog('Response from Android:', res, name)
                            if (typeof res == 'string') {
                                try {
                                    resolve(JSON.parse(res))
                                } catch (e) {
                                    resolve(res)
                                }
                            } else {
                                resolve(res)
                            }
                        }
                    )
                    return
                case PlatformType.IOS:
                    setupWKWebViewJavascriptBridge((bridge: any) => {
                        bridge.callHandler(name, params, (res: T) => {
                            window.ccLog('Response from IOS:', res, name)
                            resolve(res)
                        })
                    })
                    return
                case PlatformType.FLUTTER:
                    dsBridge.call(name, params, (res: T) => {
                        window.ccLog('Response from FLUTTER:', res)
                        resolve(res)
                    })
                    setTimeout(() => {
                        cat.audio.playEffect(AudioEffectConstant.SILENT)
                    }, 1)
                    setTimeout(() => {
                        cat.audio.playEffect(AudioEffectConstant.SILENT)
                    }, 50)
                    return

                default:
                // window.parent.postMessage({ type: name, data: params }, '*')
            }
        })
    }

    closeGame(
        type: GameCloseType = GameCloseType.GameOver,
        payload: string = ''
    ) {
        if (WECHAT) {
            cat.gui.hideLoading()
            cat.platform.leaveGame()
            return
        }
        game.pause()
        const forceReload = type === GameCloseType.JoinOverTime
        return this.callHandler(EnumJSBridgeClient.CLOSE_GAME, {
            type,
            payload,
            forceReload,
        })
    }

    disableTouch(disable: boolean) {
        // window.ccLog('注释掉 disableTouch')
        return this.callHandler(EnumJSBridgeClient.DISABLE_TOUCH, { disable })
    }

    gameLoadFinished() {
        if (!WECHAT) {
            game.pause()
            return this.callHandler(EnumJSBridgeClient.GAME_LOAD_FINISHED)
        }
    }

    openProfile(params: { userId: number }) {
        return this.callHandler(EnumJSBridgeClient.OPEN_PROFILE, params)
    }

    getAudioState() {
        return this.callHandler<AudioState>(EnumJSBridgeClient.GET_AUDIO_STATE)
    }

    /**
     * 禁用/启用聊天功能
     * @param disable true-禁用 false-允许
     */
    disableChat(disable: boolean) {
        return this.callHandler(EnumJSBridgeClient.DISABLE_CHAT, { disable })
    }

    /**
     * AI绘图开关控制
     * @param params 参数参考5.6.1章节
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ
     */
    aiDrawSwitch(params: any) {
        return this.callHandler(EnumJSBridgeClient.AI_DRAW_SWITCH, params)
    }

    /**
     * 执行AI绘图操作
     * @param params 参数参考5.6.1章节
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ
     */
    aiDraw(params: any) {
        return this.callHandler(EnumJSBridgeClient.AI_DRAW, params)
    }

    /**
     * 显示换词界面及数据
     * @param params 参数参考5.5.1章节
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ
     */
    showChangeWord(params: any) {
        return this.callHandler(EnumJSBridgeClient.SHOW_CHANGE_WORD, params)
    }

    /**
     * 执行换词操作
     * @param params 参数参考5.5.2章节
     * @see https://doc.yutang.work/docs/KrkEVzv7b8c8w2AJ
     */
    changeWord(params: any) {
        return this.callHandler(EnumJSBridgeClient.CHANGE_WORD, params)
    }

    /**
     * 调用平台API
     * @param params 包含:
     * - url: API路径 如"/japi/yoo/partyGame/op/aiDrawSwitch"
     * - params: 具体请求参数
     */
    async callPlatformApi<T>(params: { url: string; params: any }): Promise<T> {
        const result = await this.callHandler<any>(
            EnumJSBridgeClient.CALL_PLATFORM_API,
            params
        )

        // 检查返回结果的 code 字段
        if (
            result &&
            typeof result === 'object' &&
            result.errorCode !== undefined &&
            result.errorCode !== 0
        ) {
            throw result
        }

        return result as T
    }

    /**
     * 获取当前麦位麦克风状态
     * @returns 返回数组包含：
     * - userId: 用户ID
     * - status: 0-禁麦且闭麦 1-开麦 2-闭麦
     */
    getMikeVoiceStatus() {
        return this.callHandler<Array<{ userId: number; status: number }>>(
            EnumJSBridgeClient.GET_MIKE_VOICE_STATUS
        )
    }

    /**
     * 控制输入框桥接状态
     * @param enable true-游戏接收输入框内容 false-不接收
     */
    enableInputBox(enable: boolean) {
        return this.callHandler(EnumJSBridgeClient.ENABLE_INPUT_BOX, { enable })
    }

    /**
     * 获取麦位用户详细信息
     * @returns 用户信息数组包含：
     * - userId: 用户ID
     * - status: 麦位状态(0/1/2)
     * - displayName: 显示名称
     * - avatar: 头像URL
     * - avatarFrame: 头像框URL
     * - mtyMemberLevel: 会员等级
     * - mikeSoundWave: 声波数据
     */
    getMikeUsersInfo() {
        return this.callHandler<
            Array<{
                userId: number
                status: number
                displayName: string
                avatar: string | null
                avatarFrame: string | null
                mtyMemberLevel: number | null
                mikeSoundWave: any | null
            }>
        >(EnumJSBridgeClient.GET_MIKE_USERS_INFO)
    }

    saveLog(content: string) {
        return this.callHandler(EnumJSBridgeClient.SAVE_LOG, content)
    }

    //播放BGM， 端负责循环播放
    playMusic(path: string) {
        return this.callHandler(EnumJSBridgeClient.PLAY_MUSIC, { path })
    }

    //播放音效， 端负责播放一次
    playAudio(path: string) {
        return this.callHandler(EnumJSBridgeClient.PLAY_AUDIO, { path })
    }

    //震动
    playVibrate(vibrateConfig: {
        list: Array<{ time: number; intensity: number }>
        interval?: number
    }) {
        //打印vibrateConfig
        window.ccLog('vibrateConfig:', vibrateConfig)
        return this.callHandler(EnumJSBridgeClient.PLAY_VIBRATE, {
            ...vibrateConfig,
            interval: vibrateConfig.interval ?? 100,
        })
    }

    openGamePropsBuy(params: { serialNo?: string; useUid?: string } = {}) {
        const defaultParams = {
            serialNo: store.user.auth.battleId,
            useUid: store.user.auth.token || '',
        }
        const mergedParams = { ...defaultParams, ...params }
        return this.callHandler(
            EnumJSBridgeClient.OPEN_GAME_PROPS_BUY,
            mergedParams
        )
    }

    /**
     * 分发助手事件
     * @param params 包含:
     * - type: 1-基础播报类型 2-LLM类型
     * - content: 事件内容
     * @param extraParameters 包含:
     * - event_type: 事件类型
     */
    dispatchAssistantEvent(
        params: { type: 1 | 2; content: string },
        extraParameters: { event_type?: string } = { event_type: '' }
    ) {
        const defaultParams = {
            battleId: store.user.auth.battleId,
            extraParameters: JSON.stringify(extraParameters),
        }
        const mergedParams = { ...defaultParams, ...params }
        return this.callHandler(
            EnumJSBridgeClient.DISPATCH_ASSISTANT_EVENT,
            mergedParams
        )
    }
}

class JSBridgeWebViewAPI extends MessageManager {
    constructor() {
        super()

        this.addEventListener(EnumJSBridgeWebView.GAME_START)
            .addEventListener(EnumJSBridgeWebView.CHANGE_AUDIO_STATE)
            .addEventListener(EnumJSBridgeWebView.PLAYMIKEEMOJI)
            .addEventListener(EnumJSBridgeWebView.CHAT_TEXT)
            .addEventListener(EnumJSBridgeWebView.MIKE_SOUND_WAVE)
            .addEventListener(EnumJSBridgeWebView.MIKE_VOICE_STATUS)
            .addEventListener(EnumJSBridgeWebView.MIKE_USERS_INFO)
            .addEventListener(EnumJSBridgeWebView.UPDATE_BALANCE)
            .addEventListener(EnumJSBridgeWebView.USER_ROOM_GAME_MSG)
    }

    private registerHandler<T>(
        name: EnumJSBridgeWebView,
        callback: (param: T) => void
    ) {
        switch (platform) {
            case PlatformType.ANDROID:
                // 注册一个方法，供 Android 调用
                ;(window as any)?.WebViewJavascriptBridge.registerHandler(
                    name,
                    callback
                )
                return

            case PlatformType.IOS:
                setupWKWebViewJavascriptBridge((bridge: any) => {
                    bridge.registerHandler(name, callback)
                })
                return

            case PlatformType.FLUTTER:
                dsBridge.register(name, callback)
                return

            default:
            // Web 平台：直接使用 postMessage 进行通信
            // window.addEventListener('message', (event) => {
            //     if (event.origin !== window.location.origin) {
            //         // 确保只接收来自当前页面的消息
            //         return;
            //     }
            //     const { type, data } = event.data;
            //     if (type === 'androidFunction') {
            //         console.log("Received data from parent:", data);
            //         // 处理 Web 与父窗口的消息
            //         // callback();
            //     }
            // }, false);
        }
    }

    addEventListener<T = void>(name: EnumJSBridgeWebView) {
        this.registerHandler(name, (param: T) => {
            window.ccLog(
                '桥接',
                name,
                param,
                this.has(name),
                this.getEvents(name)?.size
            )
            if (name === EnumJSBridgeWebView.GAME_START) {
                game.resume()
            }
            this.dispatchEvent(name, param)
        })
        return this
    }
}

export const JSBridgeClient = new JSBridgeClientAPI()
export const JSBridgeWebView = new JSBridgeWebViewAPI()

export const closeGameToServer = () => {
    // return ws.request(ClientSocketConstant.ABNORMALEXIT, game.v1.AbnormalExitRequest, common.v1.EmptyResponse, { battle_id: store.user.auth.battle_id })
}
