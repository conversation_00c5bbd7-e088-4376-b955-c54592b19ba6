import { HTML5, WECHAT } from 'cc/env'
import AdverWX from './adverWX'
import { ADType, BaseAdver } from './BaseAdver'
import store from '../store'
import { CustomPlatform } from '../store/global'
import { Game, game } from 'cc'
import { GameCodeConstant } from '../constant/GameCodeConstant.b'

export enum ADName {
    BANNER = GameCodeConstant.WX_AD_BANNER,
    INTERSTITIAL = GameCodeConstant.WX_AD_INTERSTITIAL,
    REWARDED = GameCodeConstant.WX_AD_REWARDED,
}

let plateforAD: BaseAdver | null = null

function Adver() {
    if (!plateforAD) {
        if (
            !GameCodeConstant.WX_AD_REWARDED ||
            GameCodeConstant.WX_AD_REWARDED.trim().length <= 0
        ) {
            console.log('AD is not init')
            return
        }

        if (WECHAT) {
            /**获取平台 */
            const winSize = wx.getWindowInfo()

            plateforAD = new AdverWX([
                {
                    type: ADType.BANNER,
                    option: {
                        adUnitId: ADName.BANNER,
                        adIntervals: 30,
                        style: {
                            left: 0,
                            top: winSize.windowHeight - 106,
                            fixed: false,
                            width: winSize.windowWidth,
                        },
                    },
                },
                {
                    type: ADType.INTERSTITIAL,
                    option: {
                        adUnitId: ADName.INTERSTITIAL,
                    },
                },
                {
                    type: ADType.REWARDED,
                    option: {
                        adUnitId: ADName.REWARDED,
                    },
                },
            ])
        }
    }

    return plateforAD
}

export const AD = Adver()
