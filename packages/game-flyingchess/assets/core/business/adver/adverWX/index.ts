import { cat } from '@/core/manager'
import { error, log } from 'cc'
import { ADInit, ADType, BaseAdver } from '../BaseAdver'
import { ADName } from '..'

export default class AdverWX extends BaseAdver {
    constructor(params: ADInit[]) {
        super()
        params.forEach((item) => {
            this.adInit(item)
        })
    }

    override adInit(params: ADInit) {
        const { option, type } = params
        if (type === ADType.INTERSTITIAL) {
            // 初始化插屏
            const ad = wx.createInterstitialAd(option)
            ad.onLoad(() => {
                // console.log('插屏 广告加载成功');
            })
            ad.onError((err) => {
                // console.log('插屏 广告加载失败===========', err);
            })
            this.interstitialAdMap.set(option.adUnitId, {
                ad,
                option: params,
            })
        } else if (type === ADType.BANNER) {
            const ad = wx.createCustomAd(option)
            this.bannerVideoAdMap.set(option.adUnitId, {
                ad,
                option: params,
            })
        } else if (type === ADType.REWARDED) {
            const ad = wx.createRewardedVideoAd(option)
            ad.onLoad(() => console.log('广告加载成功'))
            // 监听错误（需捕获，否则报错）
            ad.onError((err) => console.error('广告加载失败', err))
            this.rewardedVideoAd.set(option.adUnitId, {
                ad,
                option: params,
            })
        }
    }
}
