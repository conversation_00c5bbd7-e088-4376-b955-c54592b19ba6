import { error, log } from 'cc'
import { ADName } from '.'
import { cat } from '@/core/manager'
import store from '../store'

export enum ADType {
    BANNER = 0,
    INTERSTITIAL,
    REWARDED,
}

// 各类型广告对应的配置
type AdOptionMap = {
    [ADType.BANNER]: WechatMinigame.CreateCustomAdOption
    [ADType.INTERSTITIAL]: WechatMinigame.CreateInterstitialAdOption
    [ADType.REWARDED]: WechatMinigame.CreateRewardedVideoAdOption
}

// 构建 AdInit 的联合类型
export type ADInit = {
    [T in ADType]: {
        type: T
        option: AdOptionMap[T]
    }
}[ADType]

export class BaseAdver {
    protected interstitialAdMap: Map<
        string,
        { option: ADInit; ad: WechatMinigame.InterstitialAd | null }
    > = new Map()

    protected rewardedVideoAd: Map<
        string,
        { option: ADInit; ad: WechatMinigame.RewardedVideoAd | null }
    > = new Map()

    protected bannerVideoAdMap: Map<
        string,
        { option: ADInit; ad: WechatMinigame.CustomAd | null }
    > = new Map()

    protected adInit(params: ADInit) {
        // 创建
    }

    /**显示插屏 */
    showInterstitialAdByAdUnitId(adUnitId: string, closeFunc?: Function) {
        let { ad, option } = this.interstitialAdMap.get(adUnitId) ?? {}
        if (ad) {
            ad.offClose()
            ad.onClose(() => {
                closeFunc && closeFunc()
            })
            ad.show()
                .then(() => {
                    console.log('展示插屏成功')
                })
                .catch((_) => {
                    ad.load().then(() =>
                        ad.show().then(() => {
                            console.log('展示插屏成功123')
                        })
                    )
                })
        } else {
            // 创建
            if (option) {
                console.log(`创建${adUnitId}插屏配置`)
                this.adInit(option)
                setTimeout(() => {
                    this.showInterstitialAdByAdUnitId(adUnitId)
                }, 100)
            } else {
                console.error(`未找到${adUnitId}插屏配置`)
            }
        }
    }

    /**销毁插屏 */
    destroyInterstitialAdByAdUnitId(adUnitId: string) {
        const interstitial = this.interstitialAdMap.get(adUnitId)
        interstitial?.ad?.destroy()
        if (interstitial?.ad) {
            interstitial.ad = null
        }
    }

    /**显示BANNER */
    showBannerAdByAdUnitId(adUnitId: string) {
        let { ad, option } = this.bannerVideoAdMap.get(adUnitId) ?? {}
        if (ad) {
            ad.show().then(() => {
                console.log('banner 广告显示')
            })
        } else {
            // 创建
            if (option) {
                log(`创建${adUnitId}BANNER配置`)
                this.adInit(option)
                this.showBannerAdByAdUnitId(adUnitId)
            } else {
                console.error(`未找到${adUnitId}BANNER配置`)
            }
        }
    }

    /**销毁BANNER */
    destroyBannerByAdUnitId(adUnitId: string) {
        const banner = this.bannerVideoAdMap.get(adUnitId)
        banner?.ad?.destroy()
        if (banner?.ad) {
            banner.ad = null
            log(`销毁BANNER${adUnitId}`)
        }
    }

    /**显示激励视频 */
    showRewardedAdByAdUnitId(adUnitId: string, rewardFunc?: Function) {
        /**显示激励视频 */
        let { ad, option } = this.rewardedVideoAd.get(adUnitId) ?? {}
        if (ad) {
            ad.offClose()
            ad.onClose((res) => {
                if (res.isEnded) {
                    // 用户看完广告，下发奖励（如积分、权限）
                    rewardFunc && rewardFunc()
                } else {
                    // 用户中途退出，提示未完成
                    cat.gui.showToast({
                        title: '需要完整观看视频才能获得奖励哦！',
                    })
                }
            })
            ad.show().catch((err) => {
                // 失败重试：先加载再展示
                ad.load().then(() => ad.show())
            })
        } else {
            // 创建
            if (option) {
                log(`创建${adUnitId}视频配置`)
                this.adInit(option)
                this.showRewardedAdByAdUnitId(adUnitId)
            } else {
                console.error(`未找到${adUnitId}视频配置`)
            }
        }
    }
}
