/**
 * @describe 全局事件注册
 * <AUTHOR>
 * @date 2023-08-30 13:41:39
 */

import { game, log, director, Game, sys } from 'cc'
import { AudioEventConstant, GlobalEventConstant } from '../constant'
import { EDITOR, EDITOR_NOT_IN_PREVIEW } from 'cc/env'
import store from '../store'

import { GameVisiable } from '../store/global'
import bridge from '../bridge'
import { Bridge } from '../bridge/Bridge'
import { cat } from '@/core/manager'

// 游戏显示事件
game.on(Game.EVENT_SHOW, () => {
    // game.resume();
    // director.resume();
    // audio.mute(false)
    store.global.showTime = Date.now()
    store.global.gameVisiable = GameVisiable.SHOW
    cat.event
        ?.dispatchEvent(GlobalEventConstant.EVENT_SHOW)
        ?.dispatchEvent(AudioEventConstant.RESUME_AUDIO)
})

// 游戏隐藏事件
game.on(Game.EVENT_HIDE, () => {
    store.global.hideTime = Date.now()
    store.global.gameVisiable = GameVisiable.HIDE
    cat.event
        ?.dispatchEvent(GlobalEventConstant.EVENT_HIDE)
        ?.dispatchEvent(AudioEventConstant.PAUSE_AUDIO)
    // game.pause();
    // director.pause();
    // audio.mute(true)
    // audio.pauseAll()
})
