import { cat } from '@/core/manager'
import store from '../store'

export default async function ToBeGetProfiles<T>(info: T[], field: keyof T) {
    cat.gui.showLoading({ title: '获取数据中' })
    // 待获取的玩家信息
    const toBeGet: string[] = []
    const profiles = store.lobby.profiles
    info.forEach((item) => {
        const userId = Number(item[field])
        if (!profiles.has(userId)) {
            toBeGet.push(`${userId}`)
        }
    })
    if (toBeGet.length) {
        const res = await cat.platform.getUserProfile({
            followerIds: toBeGet.join(),
        })
        res.forEach((item) => {
            if (item.id && !profiles.has(item.id)) {
                profiles.set(item.id, item)
            }
        })
    }
    cat.gui.hideLoading()
}
