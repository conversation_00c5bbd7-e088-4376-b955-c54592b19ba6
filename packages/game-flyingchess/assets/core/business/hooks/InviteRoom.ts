import store from '../store'
import { WECHAT } from 'cc/env'
import { cat } from '@/core/manager'

//微信小游戏邀请后，被邀请方点击消息后打开小游戏，如果小游戏已经打开了，就会走这个逻辑
//如果小程序没打开，则会在loading.ts 处理邀请的逻辑
if (WECHAT) {
    wx.onShow(async (opt) => {
        // 无邀请参数
        console.log('无邀请参数', !opt?.query?.room_id)
        if (!opt?.query?.room_id) return

        // 游戏中
        if (store.room.roomInfo?.gameStatus === 2) return
        // 相同房间
        if (opt?.query?.room_id === store.room.roomSerial) return
        // 在房间
        if (store.room.roomSerial) {
            await cat.api.room.quit_room({
                accessToken: store.login.access_token,
                roomSerial: store.room.roomSerial,
            })
            await cat.YXSDK.exitRoom()
        }
        const { room_id = '' } = opt.query
        // 加入房间
        try {
            await cat.api.room.join_friend({
                accessToken: store.login.access_token,
                roomSerial: room_id,
            })
            await cat.YXSDK.enterRoom()
            store.room.roomSerial = room_id
            cat.gui.loadScene('room')
        } catch (err) {
            // cat.gui.showToast({ title: '房间链接已过期，快去自己开一局吧！' });
            cat.gui.loadScene('lobby')
        }
    })
}
