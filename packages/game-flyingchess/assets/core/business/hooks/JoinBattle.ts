/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { error, log } from 'cc'
import { ToastType } from '../../components/toast/Toast'
import store from '../store'
import { cat } from '@/core/manager'
import { PitayaError } from 'pitayaclient'
import { GameCloseType, JSBridgeClient } from '../jsbridge/JSBridge'
import { WECHAT } from 'cc/env'

type JoinBattleOptions = { isReload: boolean }

/**加入战局 */
export default async function JoinBattle(isReload: boolean = false) {
    const isAudience = store.user.isAudience
    let title: string
    if (isAudience) {
        title = isReload ? '加入观战中' : '等待游戏开局...'
    } else {
        title = isReload ? '加入战局中' : '等待其他玩家加入...'
    }

    cat.gui.showLoading({ title })

    try {
        await cat.util.commontils.retryRequest(async () => {
            if (isAudience) {
                await cat.ws.Watch()
                window.ccLog('加入观战')
            } else {
                await cat.ws.JoinBattle()
                window.ccLog('加入战局')
            }
        })
    } catch (err) {
        cat.ws.destroy()
        const { msg, code } = err as PitayaError
        if (!WECHAT) {
            cat.gui.showToast({
                title: `${code}：游戏异常`,
                type: ToastType.FIXED,
                fixed_time: 2.0,
            })
        }

        if (isAudience) {
            error('加入观战错误:', code, msg)
        } else {
            error('加入战局错误:', code, msg)
        }
        // if (code === "403") { //访问不存在的战局 也会是403code
        //     // 将玩家变为观众
        //     JoinBattle({ isAudience: store.user.isAudience, isReload: false })
        //     return
        // } else {
        JSBridgeClient.closeGame(
            GameCloseType.JoinOverTime,
            JSON.stringify({ code, msg, info: '加入战局错误' })
        )
        // }
    }
}
