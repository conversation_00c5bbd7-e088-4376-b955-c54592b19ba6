import { cat } from '@/core/manager'
import store from '../store'
import { GameCodeConstant } from '../constant/GameCodeConstant.b'

type IShare = {
    title?: string
    imageUrl?: string
}

export default function Share() {
    // 分享设置
    const title = GameCodeConstant.INVITE_DESCRIPTION
    const imageUrl = GameCodeConstant.INVITE_PICTURE
    const imageUrlId = GameCodeConstant.INVITE_PICTURE_ID
    wx.onShareAppMessage(() => {
        return {
            title,
            imageUrl,
            imageUrlId,
        }
    })

    wx.onShareTimeline(() => {
        return {
            title,
            imageUrl,
            imageUrlId,
        }
    })
}
