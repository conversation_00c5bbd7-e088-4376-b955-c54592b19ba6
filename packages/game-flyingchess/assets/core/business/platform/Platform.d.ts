/**
 * @describe 小游戏API
 * <AUTHOR>
 * @date 2023-08-02 19:58:28
 */

type NavigateToMiniProgramOption = {
    appid?: string
    path?: string
    extraData?: Record<string, any>
}

type UserInfo = {
    userId?: number
    nickName?: string
    avatar?: string
    gender?: 0 | 1 | 2
    detailItems?: Array<{
        name: string
        value: string
        unit: string
    }>
    gameLevel?: string
    tierLevel?: string
    roomSerial?: string
    pointD10?: number
}

type ShareOption = {
    title?: string
    imageUrl?: string
    query?: string
}
interface MinimageAPI extends SuiLeYooAPI {
    /** 获取环境
     * @return {string} release生产环境 trial体验环境 develop开发环境
     * */
    getEnvironmentAsync(): string | Promise<string>

    /**
     * 获取小游戏冷启动时的参数
     */
    getLaunchOptionsSync(): WechatMinigame.LaunchOptionsGame | null | undefined

    /**
     * 获取授权设置
     */
    getSetting(): Promise<void>
    /**
     * 获取系统信息
     */
    getSystemInfo(): Promise<WechatMinigame.SystemInfo | void>

    /**获取登录code */
    getLoginCode(): Promise<string>

    /**获取用户信息 */
    getUserInfo(params?: IBridge.UserGameDataReq): Promise<void>

    /**跳转小程序 */
    navigateToMiniProgram(
        option?: NavigateToMiniProgramOption
    ): Promise<TaoBaoMinigame.GeneralCallbackResult | void>

    /**服务器登录 */
    serverLogin(): Promise<void>

    /**获取appId */
    getAppId(): string | void

    /**授权 */
    authorize(): Promise<void>

    /**显示分享菜单 */
    showShareMenu(): void

    // /**分享app */
    // onShareAppMessage(option: ShareOption): void

    /**分享app */
    shareAppMessage(option?: ShareOption): void

    /**分享朋友圈 */
    onShareTimeline(option: ShareOption): void

    // /**离开游戏 */
    leaveGame(): Promise<void>
}

/**基于随乐游通信接口 */
interface SuiLeYooAPI {
    /**分享*/
    share(option?: IBridge.ShareOption): void
    /**举报 */
    report(): void
    /**复制文本 */
    copyText(text: string): void
    /**打开聊天室时间 */
    roomOpenTime(params: string): Promise<void>
    /**登录聊天室 */
    loginChatRoom(params: IBridge.MatchRoomRes): Promise<void>
    /**退出聊天室 */
    logoutChatRoom(): this
    /**返回/退出 */
    back(): void
    /**任务 */
    task(): void
    /**商店 */
    mall(): void
    /**观众席 */
    audience(): void
    /**匹配 */
    matchRoom(params: IBridge.MatchRoomReq): Promise<IBridge.MatchRoomRes>
    /**获取资产 */
    getAssets(): Promise<void>
    /**搜索房间 */
    searchRoom(params: IBridge.SearchRoomReq): Promise<IBridge.SearchRoomRes>
    /**创建房间 */
    createRoom(params: IBridge.CreateRoomReq): Promise<IBridge.CreateRoomRes>
    /**充值 */
    recharge(): void

    /**获取用户简介 */
    getUserProfile(params: IBridge.QueryUserInfoReq): Promise<IBridge.Profile[]>

    /**获取麦位列表 */
    getMikeList(params: IBridge.MikeListReq): Promise<IBridge.MikeListRes>

    /**获取观众列表 */
    getAudienceList(
        params: IBridge.AudienceListReq
    ): Promise<IBridge.AudienceListRes>

    /**加入麦位 */
    joinChangeMike(params: IBridge.ChangeMikeReq): Promise<void>

    /**打开简介 */
    openProfile(params: { userId: number }): void

    /**打开聊天 */
    openPrivateChat(params: { userId: number }): void

    /*邀请 */
    invite(): void

    /**准备 */
    ready(params: IBridge.ReadyReq): Promise<void>

    /**取消准备 */
    unReady(params: IBridge.UnReadyReq): Promise<void>

    /**全局横幅特效 */
    banner(params: IBridge.BannerReq): Promise<void>
    /**获取房间信息 */
    getRoomInfo(params: IBridge.RoomInfoReq): Promise<IBridge.RoomInfoRes>

    /**打开礼物 */
    openGift(): void

    /**声音 */
    voiceState(params: IBridge.MusicState): Promise<void>

    /**麦克风 */
    micState(params: IBridge.MicState): Promise<void>

    /**音效 */
    musicState(params: IBridge.MusicState): Promise<void>

    /**发送聊天消息 */
    sendMessage(text: string): void

    /**键盘模式 */
    inputMode(mode: IBridge.InputMode): void

    /**添加好友(关注) */
    follow(params: IBridge.FollowReq): Promise<void>

    /**批量查询关注用户 */
    checkFollowedUser(
        params: IBridge.QueryFollowedUserReq
    ): Promise<IBridge.QueryFollowedUserRes>

    /**退出房间 */
    exitRoom(params: IBridge.ExitRoomReq): Promise<void>

    updateMike(params: IBridge.MikeListRes): void

    /**更多弹框 */
    moreDialog(): void

    /**麦位站起 */
    mikeUp(ddRid: string): Promise<void>

    /**坐下并准备 */
    readyDown(ddRid: string): Promise<void>

    /**踢人 */
    ban(params: IBridge.BanReq): Promise<void>
    /**锁麦 */
    mikeLock(params: IBridge.MikeLockReq): Promise<void>
    /**解麦 */
    mikeUnlock(params: IBridge.MikeUnLockReq): Promise<void>

    startGame(ddRid: string): Promise<void>

    /**语音识别连接wss */
    asrConnect(state: boolean): void

    /**语音识别开关 */
    asrOpen(state: boolean): void

    /**埋点事件 */
    clickEvent(params: IBridge.ClickEvent): void

    //#region V2

    /**加入竞技场 */
    joinArena(params: IBridge.JoinArenaReq): Promise<IBridge.JoinArenaRes>

    /**竞技场取消匹配 */
    cancelJoinArena(
        params: IBridge.CancelArenaReq
    ): Promise<IBridge.CancelArenaRes>

    /**排行榜-段位榜 */
    tierRank(params: IBridge.TierRankReq): Promise<IBridge.TierRankRes>
    /**排行榜-达人榜 */
    levelRank(params: IBridge.LevelRankReq): Promise<IBridge.LevelRankRes>
    /**房间列表 */
    roomList(params: IBridge.RoomInfoReq): Promise<IBridge.RoomListRes>
    /**游戏达人房间列表 */
    roomExpertList(
        params: IBridge.GameExpertReq
    ): Promise<IBridge.GameExpertRes>
    /**休闲场 快速匹配 */
    quickJoin(params: IBridge.QuickJoinReq): Promise<IBridge.QuickJoinRes>
    /**取消匹配 */
    cancelQuickJoin(
        params: IBridge.CancelQuickJoinReq
    ): Promise<IBridge.CancelQuickJoinRes>
    /**赛季结算 */
    seasonSettle(
        params: IBridge.SeasonSettleReq
    ): Promise<IBridge.SeasonSettleRes>
    /**点赞 */
    like(params: IBridge.LikeReq): Promise<void>

    /**长链接 */
    longLink(params: IBridge.LongLinkReq): Promise<IBridge.LongLinkRes>

    /**修改匹配状态 */
    modifyJoinState(
        params: IBridge.ModifyJoinStateReq
    ): Promise<IBridge.ModifyJoinStateRes>

    /**挂起 */
    hangUpGame(): void

    /**坐下 */
    sitDown(params: IBridge.SitDownReq): Promise<IBridge.SitDownRes>

    /**游戏状态数据 */
    gameStateInfo(
        params: IBridge.GameStateInfoReq
    ): Promise<IBridge.GameStateInfoRes>

    /**游戏状态数据 */
    taskReceive(params: IBridge.TaskReceiveReq): Promise<IBridge.TaskReceiveRes>

    /**任务列表 */
    taskList(params: IBridge.TaskReq): Promise<IBridge.TaskRes>

    /**接受上麦 */
    inviteAccept(params: IBridge.InviteAcceptReq): Promise<void>

    /**拒绝上麦 */
    inviteRefuse(params: IBridge.InviteRefuseReq): Promise<void>

    //#endregion
}
