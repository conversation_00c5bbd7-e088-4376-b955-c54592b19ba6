/**
 * @describe H5 API
 * <AUTHOR>
 * @date 2023-08-02 19:59:27
 */

import { error, log, sys } from 'cc'
import store from '../../store'

import { GlobalEventConstant } from '@/core/manager/constant'
import { cat, Manager } from '@/core/manager'
import { fromJsonString } from '@bufbuild/protobuf'
import { ClientBootParamSchema } from 'sgc'

export class H5API implements MinimageAPI {
    protected cat: Manager

    constructor(cat: Manager) {
        this.cat = cat
        this.netWorkStatusListener()
    }

    inviteAccept(params: IBridge.InviteAcceptReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    inviteRefuse(params: IBridge.InviteRefuseReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    taskReceive(
        params: IBridge.TaskReceiveReq
    ): Promise<IBridge.TaskReceiveRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    taskList(params: IBridge.TaskReq): Promise<IBridge.TaskRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    gameStateInfo(
        params: IBridge.GameStateInfoReq
    ): Promise<IBridge.GameStateInfoRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    sitDown(params: IBridge.SitDownReq): Promise<IBridge.SitDownRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    hangUpGame() {
        console.error('Method not implemented.')
        return
    }
    /**长链接 */
    longLink(params: IBridge.LongLinkReq): Promise<IBridge.LongLinkRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    /**修改匹配状态 */
    modifyJoinState(
        params: IBridge.ModifyJoinStateReq
    ): Promise<IBridge.ModifyJoinStateRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    joinArena(params: IBridge.JoinArenaReq): Promise<IBridge.JoinArenaRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    cancelJoinArena(
        params: IBridge.CancelArenaReq
    ): Promise<IBridge.CancelArenaRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    tierRank(params: IBridge.TierRankReq): Promise<IBridge.TierRankRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    levelRank(params: IBridge.LevelRankReq): Promise<IBridge.LevelRankRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    roomList(params: IBridge.RoomListReq): Promise<IBridge.RoomListRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    roomExpertList(
        params: IBridge.GameExpertReq
    ): Promise<IBridge.GameExpertRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    quickJoin(params: IBridge.QuickJoinReq): Promise<IBridge.QuickJoinRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    cancelQuickJoin(
        params: IBridge.CancelQuickJoinReq
    ): Promise<IBridge.CancelQuickJoinRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    seasonSettle(
        params: IBridge.SeasonSettleReq
    ): Promise<IBridge.SeasonSettleRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    like(params: IBridge.LikeReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    clickEvent(params: IBridge.ClickEvent): void {
        return
    }

    asrConnect(state: boolean): void {
        console.error('Method not implemented.')
        return
    }

    asrOpen(state: boolean): void {
        console.error('Method not implemented.')
        return
    }

    startGame(ddRid: string): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    ban(params: IBridge.BanReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    mikeLock(params: IBridge.MikeLockReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    mikeUnlock(params: IBridge.MikeLockReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    mikeUp(ddRid: string): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    readyDown(ddRid: string): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    getAudienceList(
        params: IBridge.AudienceListReq
    ): Promise<IBridge.AudienceListRes> {
        return Promise.reject()
    }

    protected netWorkStatusListener() {
        window.addEventListener('online', () => {
            this.online()
        })
        window.addEventListener('offline', () => {
            this.offline()
        })
    }

    protected online() {
        window.ccLog('online')
        this.cat.event.dispatchEvent(GlobalEventConstant.ONLINE)
    }

    protected offline() {
        window.ccLog('offline')
        this.cat.event.dispatchEvent(GlobalEventConstant.OFFLINE)
    }

    createRoom(params: IBridge.CreateRoomReq): Promise<IBridge.CreateRoomRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    roomOpenTime(params: string): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    openPrivateChat(params: { userId: number }): void {
        console.error('Method not implemented.')
    }
    moreDialog(): void {
        console.error('Method not implemented.')
    }
    updateMike(params: IBridge.MikeListRes): void {
        console.error('Method not implemented.')
    }

    exitRoom(params: IBridge.ExitRoomReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    checkFollowedUser(
        params: IBridge.QueryFollowedUserReq
    ): Promise<IBridge.QueryFollowedUserRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    follow(params: IBridge.FollowReq): Promise<void> {
        return Promise.resolve()
    }
    inputMode(mode: IBridge.InputMode): void {
        console.error('Method not implemented.')
    }
    loginChatRoom(params: IBridge.MatchRoomRes): Promise<void> {
        return Promise.resolve()
    }
    sendMessage(text: string): void {
        console.error('Method not implemented.')
    }
    voiceState(params: IBridge.VoiceState): Promise<void> {
        return Promise.resolve()
    }
    micState(params: IBridge.MicState): Promise<void> {
        return Promise.resolve()
    }
    musicState(params: IBridge.MusicState): Promise<void> {
        return Promise.resolve()
    }
    openGift(): void {
        console.error('Method not implemented.')
    }
    getRoomInfo(params: IBridge.RoomInfoReq): Promise<IBridge.RoomInfoRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    banner(params: IBridge.BannerReq): Promise<void> {
        return Promise.resolve()
    }
    ready(params: IBridge.ReadyReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    unReady(params: IBridge.ReadyReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    invite(): void {
        window.ccLog(`邀请好友`)
    }
    openProfile(params: { userId: number }): void {
        window.ccLog(`打开${params}个人简介`)
    }
    joinChangeMike(params: IBridge.ChangeMikeReq): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    getMikeList(room_id: IBridge.MikeListReq): Promise<IBridge.MikeListRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    getUserProfile(
        params: IBridge.QueryUserInfoReq
    ): Promise<IBridge.Profile[]> {
        console.error('Method not implemented.')
        return Promise.reject()
    }

    getEnvironmentAsync(): Promise<string> | string {
        const url = `${window.location.origin}${window.location.pathname} `
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                window.ccLog('获取环境')
                if (store.global.url_params.env) {
                    resolve(store.global.url_params.env)
                    return
                }

                const nonce =
                    url.indexOf('dev') !== -1 ||
                    url.indexOf('local') !== -1 ||
                    url.indexOf('192.168') !== -1
                        ? 'dev'
                        : url.indexOf('test') !== -1
                        ? 'trial'
                        : 'release'
                resolve(nonce)
            }, 1000)
        })
    }

    getLaunchOptionsSync(): WechatMinigame.LaunchOptionsGame | null {
        console.error('暂未实现方法')
        return null
    }
    getSetting(): Promise<void> {
        console.error('暂未实现方法')
        return Promise.resolve()
    }

    getSystemInfoSync(): null {
        console.error('暂未实现方法')
        return null
    }

    async getLoginCode(): Promise<string> {
        return new Promise((resolve, reject) => {
            resolve('0f3Ny4000ufYVQ1phi100myyjh3Ny40G')
        })
    }

    async serverLogin(): Promise<void> {
        const url_params = this.cat.util.stringUtil.getURLParameters(
            decodeURIComponent(window.location.href)
        )

        const authParams = fromJsonString(
            ClientBootParamSchema,
            url_params.params
        )

        store.user.auth = authParams

        window.ccLog('authParams', url_params, authParams)
        // await this.getLoginCode()
        // const { login } = store
        // await api.login.login(new LoginRequest({ code: login.code }))
    }

    getUserInfo(params?: IBridge.UserGameDataReq) {
        return new Promise<void>((resolve, reject) => {
            resolve()
        })
    }

    /**跳转小程序 */
    navigateToMiniProgram(): Promise<void> {
        console.error('暂未实现方法')
        return Promise.resolve()
    }

    getSystemInfo(): Promise<WechatMinigame.SystemInfo | void> {
        console.error('暂未实现方法')
        return Promise.resolve()
    }

    getAppId() {
        console.error('暂未实现方法')
        return
    }

    authorize() {
        console.error('暂未实现方法')
        return Promise.resolve()
    }

    showShareMenu() {
        console.error('暂未实现方法')
    }

    // /**分享app */
    // onShareAppMessage(option: ShareOption) {
    //     console.error('暂未实现方法')
    // }

    /**分享app */
    shareAppMessage(option: ShareOption) {
        console.error('暂未实现方法')
    }

    /**分享朋友圈 */
    onShareTimeline(option: ShareOption) {
        console.error('暂未实现方法')
    }

    share(option: IBridge.ShareOption): void {
        console.error('Method not implemented.')
    }
    report(): void {
        console.error('Method not implemented.')
    }
    copyText(copyText: string): void {
        console.error('Method not implemented.')
    }
    logoutChatRoom(): this {
        window.ccLog('logged out')
        return this
        // console.error("Method not implemented.");
    }
    back(): void {
        console.error('Method not implemented.')
    }
    task(): void {
        console.error('Method not implemented.')
    }
    mall(): void {
        console.error('Method not implemented.')
    }
    audience(): void {
        console.error('Method not implemented.')
    }

    leaveGame(): Promise<void> {
        return Promise.resolve()
    }

    matchRoom(params: IBridge.CreateRoomReq): Promise<IBridge.CreateRoomRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    getAssets(): Promise<void> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    searchRoom(params: IBridge.SearchRoomReq): Promise<IBridge.CreateRoomRes> {
        console.error('Method not implemented.')
        return Promise.reject()
    }
    recharge(): void {
        console.error('Method not implemented.')
    }
}
