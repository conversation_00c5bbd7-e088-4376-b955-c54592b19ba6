/**
 * @describe H5 API
 * <AUTHOR>
 * @date 2023-08-02 19:59:27
 */

import { fromJsonString } from '@bufbuild/protobuf'
import { log } from 'cc'
import { ClientBootParamSchema } from 'sgc'
import store from '../../store'
import { H5API } from '../H5API'

export class DaiDaiAPI extends H5API {
    override getEnvironmentAsync(): string {
        const url = `${window.location.origin}${window.location.pathname}`
        window.ccLog('url', url)
        // return window.location.host.search(/.com/) != -1 ? 'release' : 'trial';
        return url.indexOf('dev') !== -1 ||
            url.indexOf('localhost') !== -1 ||
            url.indexOf('192.168') !== -1
            ? 'dev'
            : url.indexOf('test') !== -1
            ? 'trial'
            : 'release'
    }

    override getLaunchOptionsSync(): null {
        console.error('暂未实现方法')
        return null
    }
    override getSetting(): Promise<void> {
        console.error('暂未实现方法')
        return Promise.resolve()
    }

    override getSystemInfoSync(): null {
        console.error('暂未实现方法')
        return null
    }

    override async getLoginCode(): Promise<string> {
        return new Promise((resolve, reject) => {
            resolve('0f3Ny4000ufYVQ1phi100myyjh3Ny40G')
        })
    }

    override async serverLogin(): Promise<void> {
        const url_params = this.cat.util.stringUtil.getURLParameters(
            decodeURIComponent(window.location.href)
        )

        const authParams = fromJsonString(
            ClientBootParamSchema,
            url_params.params
        )

        store.user.auth = authParams

        window.ccLog('authParams', url_params, authParams)
        // window.ccLog('token', token)
        // token && (store.login.token = token)
        // await this.getLoginCode()
        // const { login } = store
        // await api.login.login(new LoginRequest({ code: login.code }))
    }
    override getUserInfo() {
        return new Promise<void>((resolve, reject) => {
            resolve()
        })
    }
    /**跳转小程序 */
    override navigateToMiniProgram(): Promise<void> {
        console.error('暂未实现方法')
        return Promise.reject()
    }

    override getSystemInfo(): Promise<void> {
        console.error('暂未实现方法')
        return Promise.reject()
    }

    override getAppId() {
        console.error('暂未实现方法')
        return
    }

    override authorize() {
        console.error('暂未实现方法')
        return Promise.reject()
    }

    override showShareMenu() {
        console.error('暂未实现方法')
    }

    /**分享app */
    override shareAppMessage(option: ShareOption) {
        console.error('暂未实现方法')
    }
    /**分享朋友圈 */
    override onShareTimeline(option: ShareOption) {
        console.error('暂未实现方法')
    }
}
