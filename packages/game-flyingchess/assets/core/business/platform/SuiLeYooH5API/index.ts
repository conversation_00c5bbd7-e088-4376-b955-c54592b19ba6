/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { ClientBootParam, ClientBootParamSchema } from 'sgc'
import { H5API } from '../H5API'
import { log } from 'cc'
import store from '../../store'
import bridge from '../../bridge'
import { Bridge } from '../../bridge/Bridge'
import { create, fromJsonString } from '@bufbuild/protobuf'
import { JSBridgeClient, JSBridgeWebView } from '../../jsbridge/JSBridge'

export class SuiLeYooH5API extends H5API {
    override async serverLogin(): Promise<void> {
        const url_params = this.cat.util.stringUtil.getURLParameters(
            decodeURIComponent(window.location.href)
        )

        const authParams = fromJsonString(
            ClientBootParamSchema,
            url_params.params
        )

        store.user.auth = authParams

        window.ccLog('authParams', url_params, authParams)
    }

    override matchRoom<T extends IBridge.MatchRoomRes>(
        params: IBridge.CreateRoomReq
    ) {
        return this.cat.api.suileyoo.matchRoom<T>(params)
    }

    override async getAssets(): Promise<void> {
        const res = await this.cat.api.suileyoo.getAssets()
        store.user.assets = res.point
    }

    override async searchRoom<T extends IBridge.SearchRoomRes>(
        params: IBridge.SearchRoomReq
    ): Promise<T> {
        return this.cat.api.suileyoo.searchRoom(params)
    }

    override async getUserInfo<T extends IBridge.UserGameDataRes>(
        params: IBridge.UserGameDataReq
    ): Promise<void> {
        const res = await this.cat.api.suileyoo.getUserInfo(params)
        store.user.userGameData = res
    }

    override async getUserProfile<T extends IBridge.Profile[]>(
        params: IBridge.QueryUserInfoReq
    ): Promise<T> {
        return await this.cat.api.suileyoo.getUserProfile(params)
    }

    override async getMikeList(
        room_id: IBridge.MikeListReq
    ): Promise<IBridge.MikeListRes> {
        return new Promise<IBridge.MikeListRes>(async (resolve, reject) => {
            const res = await this.cat.api.suileyoo.getMikeList(room_id)
            bridge.nativeAPI.dispatchEvent(Bridge.EventName.APIName.MIKE_LIST, {
                code: 0,
                data: res,
            })
            resolve(res)
        })
    }

    async joinMike(params: IBridge.ChangeMikeReq) {
        // return await this.cat.api.suileyoo.joinMike(params)
    }

    override async ready(params: IBridge.ReadyReq): Promise<void> {
        return await this.cat.api.suileyoo.ready(params)
    }

    override async unReady(params: IBridge.UnReadyReq): Promise<void> {
        return await this.cat.api.suileyoo.unReady(params)
    }

    override async getRoomInfo(
        params: IBridge.RoomInfoReq
    ): Promise<IBridge.RoomInfoRes> {
        return new Promise<IBridge.RoomInfoRes>(async (resolve, reject) => {
            const res = await this.cat.api.suileyoo.getRoomInfo(params)
            // store.match.roomInfo = res
            // TODO TEST
            bridge.nativeAPI.dispatchEvent(Bridge.EventName.APIName.ROOM_INFO, {
                code: 0,
                data: res,
            })

            resolve(res)
        })
    }

    override exitRoom(params: IBridge.ExitRoomReq): Promise<void> {
        return this.cat.api.suileyoo.exitRoom(params)
    }

    override openProfile(params: { userId: number }) {
        JSBridgeClient.openProfile(params)
    }
}
