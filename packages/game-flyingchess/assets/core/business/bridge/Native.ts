/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

/**桥接基础 */

import { error, log, native } from 'cc'

import { ANDROID, IOS } from 'cc/env'
import { Bridge } from './Bridge'
import { MessageManager } from '@/core/manager/event'

type MethodNameMap = {
    [Bridge.MethodName.NativeBridge
        .CALLBACK_API_RESPONSE]: Bridge.EventName.APIName
    [Bridge.MethodName.NativeBridge
        .CALLBACK_BRIDGE_RESPONSE]: Bridge.EventName.BridgeName
    [Bridge.MethodName.NativeBridge
        .CALLBACK_COMMON_RESPONSE]: Bridge.EventName.CommonName
    [Bridge.MethodName.NativeBridge
        .CALLBACK_CHATROOM_RESPONSE]: Bridge.EventName.ChatRoomName
    // 其他桥接类型和对应的枚举类型...
}

type NativeResponse<T extends Bridge.MethodName.NativeBridge> = {
    name: MethodNameMap[T]
    data: any
}

interface INativeBridge {
    [Bridge.MethodName.NativeBridge.CALLBACK_API_RESPONSE]: (
        res: string
    ) => void
    [Bridge.MethodName.NativeBridge.CALLBACK_BRIDGE_RESPONSE]: (
        res: string
    ) => void
    [Bridge.MethodName.NativeBridge.CALLBACK_COMMON_RESPONSE]: (
        res: string
    ) => void
    [Bridge.MethodName.NativeBridge.CALLBACK_CHATROOM_RESPONSE]: (
        res: string
    ) => void
}

/**Native调用Cocos*/
export class NativeAPI extends MessageManager implements INativeBridge {
    [Bridge.MethodName.NativeBridge.CALLBACK_API_RESPONSE](res: string) {
        window.ccLog('API接口桥接返回', res)
        const parsedRes: NativeResponse<Bridge.MethodName.NativeBridge.CALLBACK_API_RESPONSE> =
            JSON.parse(res)
        const data: IBridge.IResponse<any> = parsedRes.data
        if ('errorCode' in data) {
            console.error(data.detailMessage)
        }
        if (this.has(parsedRes.name)) this.dispatchEvent(parsedRes.name, data)
    }

    [Bridge.MethodName.NativeBridge.CALLBACK_BRIDGE_RESPONSE](res: string) {
        window.ccLog('BRIDGE接口桥接返回', res)
        const parsedRes: NativeResponse<Bridge.MethodName.NativeBridge.CALLBACK_BRIDGE_RESPONSE> =
            JSON.parse(res)
        const { name, data } = parsedRes
        if (this.has(name)) this.dispatchEvent(name, data)
    }

    [Bridge.MethodName.NativeBridge.CALLBACK_COMMON_RESPONSE](res: string) {
        window.ccLog('COMMON接口桥接返回', res)
        const parsedRes: NativeResponse<Bridge.MethodName.NativeBridge.CALLBACK_COMMON_RESPONSE> =
            JSON.parse(res)
        const { name, data } = parsedRes
        if (this.has(name)) this.dispatchEvent(name, data)
    }
    [Bridge.MethodName.NativeBridge.CALLBACK_CHATROOM_RESPONSE](res: string) {
        window.ccLog('CHATROOM接口桥接返回', res)
        const parsedRes: NativeResponse<Bridge.MethodName.NativeBridge.CALLBACK_CHATROOM_RESPONSE> =
            JSON.parse(res)
        const { name, data } = parsedRes
        if (this.has(name)) this.dispatchEvent(name, data)
    }
}
