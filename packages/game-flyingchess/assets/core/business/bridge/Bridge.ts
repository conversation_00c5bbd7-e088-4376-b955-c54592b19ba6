/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

export namespace Bridge {
    /**桥接方法名 */
    export namespace MethodName {
        export enum CocosBridge {
            /**接口 */
            LOAD_API_REQUEST = 'loadApiDataInfo',
            /**桥接 */
            LOAD_BRIDGE_REQUEST = 'loadBridgeAction',
            /**通用设置 */
            LOAD_COMMON_REQUEST = 'loadCommonAction',
            /**聊天室 */
            LOAD_CHATROOM_REQUEST = 'loadChatRoomAction',
        }

        export enum NativeBridge {
            /**接口 */
            CALLBACK_API_RESPONSE = 'callBackApiData',
            /**桥接 */
            CALLBACK_BRIDGE_RESPONSE = 'callBackBridgeData',
            /**通用设置 */
            CALLBACK_COMMON_RESPONSE = 'callBackCommonData',
            /**聊天室 */
            CALLBACK_CHATROOM_RESPONSE = 'callBackChatRoomData',
        }
    }

    /**桥接事件名 */
    export namespace EventName {
        /**接口名称 */
        export enum APIName {
            /**创建房间 */
            CREATE = 'japi/yoo/partyGame/create',
            /**搜索房间 */
            SEARCH = 'japi/yoo/partyGame/search',
            /**匹配房间 */
            MATCHING = 'japi/yoo/partyGame/matching',
            /**派对游戏详情 */
            DETAIL = 'japi/yoo/game/party/detail',
            /**用户资产 */
            ASSETS = 'japi/yoo/partyGame/user/assets',
            /**房间基础信息接口 */
            ROOM_INFO = 'japi/yoo/partyGame/roomInfo',
            /**观众接口 */
            VIEW_LIST = 'japi/yoo/partyGame/mike/viewers',
            /**观众列表 */
            USER_LIST = 'japi/yoo/partyGame/viewers',
            /**麦位列表 */
            MIKE_LIST = 'japi/yoo/partyGame/mike/list',
            /**站起 */
            MIKE_UP = 'japi/yoo/partyGame/mike/down',
            /**个人游戏数据 */
            USER_GAME_DATA = 'japi/yoo/partyGame/user/gdInfo',
            /**查询用户信息 */
            CHECK_USER_PROFILES = 'japi/social/fw/profiles/v4',
            /**准备 */
            READY = 'japi/yoo/partyGame/mike/ready',
            /**取消准备 */
            UNREADY = 'japi/yoo/partyGame/mike/unReady',
            /**退出房间 */
            EXIT = 'japi/yoo/v2/room/party/game/exit',
            /**切换麦位(上麦) */
            MIKE_CHANGE_JOIN = 'japi/yoo/partyGame/mike/up',
            /**坐下并准备 */
            READY_DOWN = 'japi/yoo/partyGame/mike/upReady',

            /**踢人 */
            BAN = 'japi/yoo/blacklist/kick',
            /**锁定麦位 */
            MIKE_LOCK = 'japi/yoo/partyGame/mike/lock',
            /**解锁麦位 */
            MIKE_UNLOCK = 'japi/yoo/partyGame/mike/unlock',
            /**开始游戏 */
            START_GAME = 'japi/yoo/partyGame/start',
            /**获取启动游戏地址 */
            ENTER_PARAM = 'japi/yoo/partyGame/enterParam',

            /**悬浮球获取游戏状态信息 */
            FLOAT_BALL = 'japi/yoo/game/floatBall',

            /*结算*/
            SETTLE_INFO = 'japi/yoo/v2/room/party/game/settle/info',

            /**查询已关注的用户 */
            CHECK_FOLLOWED = 'japi/social/fw/followedUids',
            /**关注 */
            FOLLOW = 'japi/social/fw/follow',

            /**获取横幅信息 */
            GET_BANNER = 'japi/msg/config/getPushConfig',

            /**道具使用 */
            ITEM_USE = 'japi/yoo/partyGame/op/useGameEvent',
            /**道具配置 */
            ITEM_CONFIG = '/japi/yoo/partyGame/op/gameEvents',

            // 游戏内操作-限制性道具配置获取
            PROP_CONFIG = '/japi/yoo/mityoo/gameProp/gameLimitEvent',

            // 游戏内操作-使用限制性道具
            PROP_USE = '/japi/yoo/mityoo/gameProp/useGameLimitEvent',

            // 游戏内操作-获取非限制性道具列表
            PROP_GAME_EVENTS = '/japi/yoo/mityoo/gameProp/gameEvents',

            // 游戏内操作-使用非限制性道具
            PROP_USE_GAME_EVENT = '/japi/yoo/mityoo/gameProp/useGameEvent',
        }

        /**V2接口 */
        export enum APIV2Name {
            /**竞技场加入 */
            JOIN_ARENA = 'japi/yoo/partyGame/arena/join',
            /**竞技场取消匹配 */
            EXIT_ARENA = 'japi/yoo/partyGame/arena/exit',
            /**排行榜-段位榜 */
            TIER_RANK = 'japi/yoo/partyGame/data/tierRank',
            /**排行榜-达人榜 */
            LEVEL_RANK = 'japi/yoo/partyGame/data/levelRank',
            /**房间列表 */
            ROOM_LIST = 'japi/yoo/partyGame/data/rooms',
            /**游戏达人房间列表 */
            ROOM_EXPERT_LIST = 'japi/yoo/partyGame/data/gameExpert',
            /**休闲场 快速匹配 */
            QUICK_JOIN = 'japi/yoo/partyGame/quickJoin',
            /**取消匹配 */
            CANCEL_QUICK_JOIN = 'japi/yoo/partyGame/cancelQuickJoin',
            /**赛季结算 */
            SEASON_SETTLE = 'japi/yoo/partyGame/data/seasonSettle',
            /**点赞 */
            LIKE = 'japi/yoo/partyGame/user/userUpvote',
            /**获取长链接 */
            LONG_LINK = 'japi/yoo/partyGame/longLink',
            /**进入世界频道 */
            JOIN_WORLD_CHANNEL = 'japi/yoo/partyGame/joinWorldChannel',
            /**修改匹配状态 */
            MODIFY_JOIN_STATE = 'japi/yoo/partyGame/modifyJoinState',
            /**坐下 */
            SIT_DOWN = 'japi/yoo/partyGame/mike/sitDown',
            /**游戏状态数据 */
            GAME_STATE_INFO = 'japi/yoo/partyGame/user/gameStateInfo',
            /**任务列表 */
            TASK = 'japi/social/tsk/partyGame/list',
            /**奖励领取 */
            TASK_RECEIVE = 'japi/social/tsk/partyGame/receive',
            /**接受上麦 */
            InviteAccept = '/japi/yoo/mike/invite/accept',
            /**拒绝上麦 */
            InviteRefuse = '/japi/yoo/mike/invite/refuse',
        }

        /**桥接名称 */
        export enum BridgeName {
            /**返回 */
            BACK = 'back',
            /**任务 */
            TASK = 'task',
            /**商城 */
            MALL = 'mall',
            /**旁观席 */
            AUDIENCE = 'audience',
            /**拉黑 */
            black = 'black',
            /**举报 */
            REPORT = 'report',
            /**分享 */
            SHARE = 'shareAction',
            /**横幅特效开关 */
            BANNER_SPECIAL = 'bannerSpecial',
            /**邀请 */
            INVITE = 'invite',
            /**礼物 */
            GIFT = 'gift',
            /**名片卡 */
            AVATAR_CARD = 'avatarCard',
            /**更多弹框 */
            MORE_DIALOG = 'moreDialog',
            /**更多弹框-规则 */
            RULE = 'rule',
            /**更多弹框-退出 */
            EXIT = 'exit',
            /**跳转私聊 */
            PRIVATE_CHAT = 'chat',
            /**分享图片 */
            SHARE_IMG = 'shareImg',
            /**点击事件埋点 */
            CLICK_EVENT = 'clickEvent',
            /**挂起 */
            HANG_UP_GAME = 'hangUpGame',
            /**商城购买 */
            MALL_BUY = 'mallBuy',
            /**关闭所有弹框 */
            CLOSE_DIALOG = 'closeDialog',
        }

        /**通用接口 */
        export enum CommonName {
            /**资源加载完成(cocos调用端) */
            RES_COMPLETE = 'resComplete',
            /**加载当前游戏(端调用cocos) */
            RES_GAME_ID = 'resGameId',
            /*音频状态 */
            VOICE_STATE = 'voiceState',
            /**游戏音效 */
            MUSIC_STATE = 'musicState',
            /**麦克风状态 */
            MIC_STATE = 'micState',
            /**传递麦位接口数据(cocos调用端) */
            UPDATE_MIKE = 'updateMikeData',
            /**复制文本 */
            COPY_TEXT = 'copyText',
            /**充值 */
            RECHARGE = 'recharge',
            /**设置键盘模式*/
            UPDATE_INPUTMODE = 'updateInputMode',
            /**声波动效状态 0 关闭 1开启 */
            SOUND_WAVE_STATE = 'soundWaveState',
            /**更新余额 */
            UPDATE_BALANCE = 'updateBalance',
            /**房间信息接口字段openTime */
            ROOM_OPEN_TIME = 'roomOpenTime',
            /**网络监听 */
            NETWORK_AVAILABLE = 'networkAvailable',
            /**语音转文本 */
            SPEECH_TO_TEXT = 'speechToText',
            /**语音识别连接wss*/
            ASRCONNECT = 'asrConnect',
            /**语音识别开关 */
            ASROPEN = 'asrOpen',
        }

        /**聊天室 */
        export enum ChatRoomName {
            /**登录聊天室 */
            LOGIN_CHAT_ROOM = 'loginChatRoom',
            /**退出聊天室 */
            LOGOUT_CHAT_ROOM = 'logoutChatRoom',
            /**发送消息 */
            SEND_MESSAGE = 'sendMessage',
            /**普通消息 */
            MESSAGE = 'message',
            /**自定义消息 */
            CUSTOM_MESSAGE = 'customMessage',
            /**消息发送成功后追加到聊天列表忽略 */
            APPEND_MESSAGE = 'appendMessage',
            /**被管理员踢出房间 */
            KICKOUT_ROOM = 'kickOutRoom',
        }
    }
}
