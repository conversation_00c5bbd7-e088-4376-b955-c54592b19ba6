
declare namespace IBridge {
    //#region 竞技场

    /**竞技场加入 */
    type JoinArenaReq = {
        gameId: number,
        /**游戏模式：1-休闲场单人赛；2-休闲场2V2；10-竞技场单人赛 */
        partyMode: number
    }

    type JoinArenaRes = {
        /**竞技场名称 */
        arenaName: string,
        /**预计匹配时间，单位秒 */
        matchTimes: number
    }

    /**竞技场取消匹配 */
    type CancelArenaReq = {
        gameId: number
    }

    type CancelArenaRes = {
        gameId: number
    }

    //#endregion


    //#region 排行榜

    /**排行榜-段位榜 */
    type TierRankReq = {
        /**游戏ID */
        gameId: number
        /**赛季ID。不指定默认当前赛季 */
        seasonId?: number | null
    }

    type TierRankRes = {
        /**赛季列表 */
        gameSeasons: GameSeason[]
        /**榜单数据 */
        rankList: UserGameSeasonRank[]
        /**用户个人段位信息 */
        userRank: UserGameSeasonRank
    }

    type GameSeason = {
        /** 赛季ID */
        id: number
        /**赛季名称 */
        name: string
        /**赛季LOGO */
        logo: string
        /**赛季标语 */
        slogan: string
        /**赛季周期。当前赛季或者yyyy.M.d - yyyy.M.d */
        seasonCycle: string
    }

    type UserGameSeasonRank = {
        /**用户ID */
        userId: number
        /**赛季ID */
        seasonId: number
        /**赛季经验值 */
        seasonExp: number
        /**当前赛季段位 */
        seasonTier: SeasonTier
    }

    /**排行榜-达人榜 */
    type LevelRankReq = {
        gameId: number
        /**榜单类型：1-总榜；2-好友。默认总榜 */
        rankType?: number
    }

    type LevelRankRes = {
        /**榜单数据 */
        rankList: UserGameLevelRank[]
        /**用户排名 */
        userRank: UserGameLevelRank
    }

    type UserGameLevelRank = {
        /**用户ID */
        userId: number
        /**游戏ID */
        gameId: number
        /**  经验值 */
        exp: number
        /** 游戏等级 */
        gameLevel: number
    }

    //#endregion


    //#region 房间列表
    /** 房间列表 */
    type RoomListReq = {
        gameId: number
    }


    type RoomListItem = {
        /**房间ID */
        ddRid: string
        /**游戏ID */
        gameId: string
        /**游戏名称 */
        gameName: string
        /**房间流水 */
        roomSerial: string
        /**房主UID */
        tempHownerId: string
        /**麦位人数 */
        mikeCount: string
        /**游戏模式：1-休闲场单人赛；2-休闲场2V2；10-竞技场单人赛*/
        partyMode: string
        /**游戏状态：0-匹配中；1-准备倒计时；2-游戏中 */
        readyStatus: string
        /**房间人数 */
        roomCount: string
    }

    type RoomListRes = RoomListItem[]

    //#endregion

    //#region 游戏达人
    type GameExpertReq = RoomListReq

    type GameExpertRes = RoomListRes

    //#endregion


    //#region 休闲场

    /**快速匹配 */
    type QuickJoinReq = {
        gameId: number
    }

    type QuickJoinRes = {
        /**预计匹配时间，单位秒 */
        matchTimes: number
    }

    /**取消匹配 */
    type CancelQuickJoinReq = {
        gameId: number
    }

    type CancelQuickJoinRes = void

    //#endregion

    //#region 赛季结算
    type SeasonSettleReq = {
        /**游戏ID */
        gameId: number
    }

    type SeasonSettleRes = {
        /**上一赛季段位数据。为null表示无上一个赛季数据 */
        lastUserSeasonInfo: UserGameSeasonTier
        /**当前赛季段位数据。为null表示当前赛季暂无开始 */
        userSeasonInfo: UserGameSeasonTier
        /**限时时段 */
        limitedTime: string
        /**显示休闲场小手引导  */
        showHand: 0 | 1
    }

    type UserGameSeasonTier = {
        /**赛季id */
        id: number
        seasonId: number
        /**用户id */
        userId: number
        /** 赛季名称*/
        name: string
        /**赛季LOGO */
        logo: string
        /**赛季周期。当前赛季或者yyyy.M.d-yyyy.M.d */
        seasonCycle: string
        /**赛季经验值 */
        seasonExp: number
        /**赛季段位 */
        seasonTier: SeasonTier
    }


    //#endregion

    //#region 点赞

    type LikeReq = {
        /**游戏ID */
        gameId: number
        /**被点赞的用户UID */
        upvoteUid: number
        /**房间ID */
        ddRid: string
        /**对战唯一标识 */
        serialNo: string
    }

    //#endregion

    //#region 长链接

    type LongLinkReq = {
        gameId: number
    }

    type LongLinkRes = {
        url: string,
    }


    //#endregion

    //#region 修改匹配状态

    type ModifyJoinStateReq = {
        ddRid: string,
        /**0-不匹配用户；1-匹配用户 */
        joinState: number
    }

    type ModifyJoinStateRes = void


    //#endregion

    //#region 坐下

    type SitDownReq = {
        ddRid: string,
    }

    type SitDownRes = void


    //#endregion

    //#region 游戏状态数据

    type GameStateInfoReq = {
        gameId: number,
    }

    type GameStateInfoRes = {
        /**游戏场景：0-游戏主页场景；1-房间页场景；2-游戏内场景；3-快速加入匹配中场景；4-竞技场匹配中场景 */
        gameSate: number
        userInfo: UserGameDataRes
        roomInfo: RoomInfoRes
        gameEnterInfo: EnterParam
        quickJoinInfo: QuickJoinRes
        arenaJoinInfo: JoinArenaRes
    }


    //#endregion

}