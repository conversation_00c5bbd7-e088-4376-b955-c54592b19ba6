declare namespace IBridge {
    /**异常响应 */
    type IErrorResponse = {
        errorCode: number
        /**错误信息 */
        detailMessage: string
        message: string
        /**输出数据 */
        data: null
    }

    /**成功响应 */
    type ISuccessResponse<T> = {
        code: number
        message: string
        /**输出数据 */
        data: T
    }

    /**通过响应格式 */
    type IResponse<T> = ISuccessResponse<T> | IErrorResponse

    //#region 游戏主页

    /**个人数据请求 */
    export type UserGameDataReq = {
        /**游戏ID */
        gameId: number
    }

    /**个人数据响应 */
    type UserGameDataRes = {
        /**当前派对游戏时长(分钟) */
        playTime: number
        /**当前派对游戏总场次 */
        playCount: number
        /**当前派对游戏胜利场次 */
        winCount: number
        /**胜率（百分比） */
        winRatio: string
        /**用户ID */
        userId: number
        /**当前游戏等级 */
        gameLevel: number
        /**当前游戏经验值 */
        exp: number
        /**当前赛季段位。当前无赛季，返回null */
        seasonTier: SeasonTier | null
        /**当前赛季经验值 */
        seasonExp: number
    }

    /**赛季 */
    type SeasonTier = {
        /**段位logo */
        logo: string
        /**段位等级图片 */
        img: string
        /**段位名称 */
        name: string
        /**等级罗马数值 */
        romanVal: string
    }

    /**1-个人赛(2-5人场) 2-组队赛（2V2）10竞技场单人 20竞技场组队*/
    type PartyMode = 0 | 1 | 2 | 10 | 20

    /**创建房间请求 */
    type CreateRoomReq = {
        /**游戏ID */
        gameId: number
        /**1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode
        /**玩家数量 */
        mikeCount: number
        /**0-不匹配用户；1-匹配用户 */
        joinState: number
    }

    /**创建房间响应 */
    type CreateRoomRes = {
        /**房间ID */
        ddRid: string
        /**房间流水 */
        roomSerial: string
        /**云信IM账号 */
        imUsername: string
        /**云信IM密码 */
        impassword: string
        /**声网token */
        swToken: string
        /**当前用户ID */
        userId: number
        /**1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode
    }

    /**搜索房间请求 */
    type SearchRoomReq = {
        /**游戏ID */
        gameId: number
        /**房间ID */
        ddRid: string
    }

    /**搜索房间响应 */
    type SearchRoomRes = CreateRoomRes

    /**匹配房间请求 */
    type MatchRoomReq = CreateRoomReq

    /**匹配房间响应 */
    type MatchRoomRes = CreateRoomRes

    //#endregion

    //#region 房间主页

    /**用户资产请求 */
    type UserAssetReq = {}

    /**用户资产响应 */
    type UserAssetRes = {
        /**云贝 */
        point: number
        /**小鱼干 */
        pointD10: number
    }

    type Extra = {
        pointD10: number
    }

    /**房间信息请求 */
    type RoomInfoReq = {
        /**游戏ID */
        gameId: number
    }

    /**房间信息响应 */
    type RoomInfoRes = {
        /**房间ID */
        ddRid: string
        /**房间流水号 */
        roomSerial: string
        /**房间名称 */
        roomName: string
        /**0未知 1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode
        /**房间公告 */
        announcement: string
        /**房间游戏状态 0-未开始（匹配中）；1-准备倒计时，2-游戏中*/
        readyStatus: 0 | 1 | 2
        /**剩余倒计时(s) */
        duration: number
        /**云信IM账号 */
        imUsername: string
        /**云信IM密码 */
        impassword: string
        /**声网token */
        swToken: string
        /**房间开启时间。格式：yyyy-MM-dd HH:mm:ss */
        openTime: string
        /**0-不匹配用户；1-匹配用户 */
        joinState: number
        /**0-非超管；1-是超管 */
        superManager: number
    }

    type BlockBanner = 0 | 1

    /**全局横幅特效响应 */
    type BannerRes = {
        /**屏蔽房间横幅动效 0、不屏蔽；1、屏蔽 */
        blockBanner: BlockBanner
    }

    /**退出房间请求 */
    type ExitRoomReq = {
        /**房间ID */
        ddRid: string
    }

    //#endregion

    //#region 麦位接口
    type MikeState = 0 | 1 | 2

    type MikeItem = {
        /**麦位ID */
        mikeId: number
        /**房间ID */
        ddRid: string
        /**麦位成员用户ID */
        userId: number
        /**麦位：房客1~5 */
        position: number
        /**队伍，从1开始 */
        team: number
        /**麦位状态：0-未锁定，1-已锁定 */
        status: 0 | 1
        /**是否被禁麦：0-否，1-是 */
        isBanVoice: 0 | 1
        /**是否开麦：0-否，1-是 */
        isOpenVoice: 0 | 1
        /**游戏状态(0-未准备,1-已准备) */
        readyStatus: MikeState
    }

    /**麦位列表请求 */
    type MikeListReq = {
        /**房间ID */
        ddRid: string
    }

    /**麦位列表响应 */
    type MikeListRes = {
        tempHownerId: number //房主UID
        mikeList: MikeItem[] //麦位列表
    }

    /**切换麦位请求 */
    type ChangeMikeReq = {
        /**目标麦位ID */
        mikeId: number
    }

    /**观众列表请求 */
    type AudienceListReq = {
        /**房间ID */
        ddRid: string
        /**页码 */
        page: number
        /**数量 */
        size: number
    }

    type IAudience = { userId: number }

    /**观众列表响应 */
    type AudienceListRes = {
        /**总记录数，必须提供 */
        totalRows: number
        /**总页数，必须提供 */
        totalPage: number
        /**第几页，必须提供 */
        pageNo: number
        /**每页条数，必须提供 */
        pageSize: number
        /**结果集，必须提供，T为具体类型 */
        rows: Array<IAudience>
        /**分页彩虹，必须提供，表示分页的颜色数组或类似用途 */
        rainbow: number[]
    }

    //#endregion

    //#region 游戏接口

    /**准备 */
    type ReadyReq = {
        /**麦位ID */
        mikeId: number
    }

    /**取消准备 */
    type UnReadyReq = ReadyReq

    /**获取进入游戏参数(启动参数) */
    type EnterParam = {
        /**麦位ID */
        battleId: string
        /**游戏模式 */
        mode: string
        /**玩家入场token（观战不需要） */
        token?: string
        serverId?: string
        aiHelperType?: number
    }
    //#endregion

    //#region 游戏结算

    /**0|未填写，1|男，2|女 */
    type Gender = 0 | 1 | 2

    type Profile = {
        /**显示昵称 */
        displayName: string
        /**头像 */
        avatar: string
        /**头像框 */
        avatarFrame: string
        /**性别 */
        gender: Gender
        /**简介 */
        intro: string
        /**铭牌地址 */
        nameplate: string
        /**徽章 */
        badge: string
        /**名片卡 */
        bizCard: string
        /**归属地区 */
        region: string
        /**id */
        id: number | undefined
        /**魅力值 */
        charms: number
    }

    /**查询用户信息请求 */
    type QueryUserInfoReq = {
        /**UID(同userId)集合，以逗号分隔。最大支持100 ("A,B...")*/
        followerIds: string
    }

    /**查询用户信息响应 */
    type QueryUserInfoRes = UserInfo[]

    /**查询已关注的用户请求 */
    type QueryFollowedUserReq = {
        /**UID(同userId)集合，需要查的人，多个用逗号分隔 ("A,B...")*/
        selectUids: string
    }

    /**查询已关注的用户响应 */
    type QueryFollowedUserRes = {
        /**已经关注的人集合 */
        followUidList: number[]
    }

    /**关注(添加好友)请求 */
    type FollowReq = {
        /*关注人UID(同userId)*/
        followerId: string
    }

    /**锁麦请求 */
    type MikeLockReq = {
        ddRid: string
        mikeId: number
    }
    /**解麦请求 */
    type MikeUnLockReq = MikeLockReq

    /**踢人请求 */
    type BanReq = {
        ddRid: string
        kickUserId: number
    }
    //#endregion

    //#region 任务
    type TaskReq = {
        gameId: number
    }

    type TaskRes = TaskItem[]

    type TaskItem = {
        /**任务标识 */
        taskCode: string
        /**任务名称 */
        taskName: string
        /**已完成进度 */
        finishProgress: number
        /**总进度 */
        progress: number
        /**状态, 0-待完成；1-待领取；2-已完成 */
        status: number
        /**奖励信息 */
        reward: GameTaskReward
    }

    type GameTaskReward = {
        /**奖励物品图片 */
        icon: string
        /**奖励数量 */
        value: number
        /**奖励物品名称 */
        name: string
        /**奖励物品描述 */
        desc: string
    }
    //#endregion

    //#region 任务领取

    type TaskReceiveReq = {
        gameId: number
        taskCode: string
    }

    type TaskReceiveRes = {
        icon: string
        value: number
        name: string
        desc: string
    }

    //#endregion

    //#region 游戏道具

    /**道具配置请求 */
    type GamePropsConfigReq = {
        /**游戏ID */
        gameId: number
    }

    type LimitPropConfigReq = {
        /**战局ID */
        serialNo: string
        event: string
    }

    type PropConfig = {
        /**消耗云贝 */
        point: number
        /**状态：1-免费；2-付费;3-次数已用完 */
        status: number
        /**付费小鱼干。status=2才有 */
        pointD10?: number
        /**总次数 */
        total: number
        /**剩余次数 */
        remain: number
        /**用户当前小鱼干 */
        userPointD10: number
    }

    type LimitPropConfigRes = IResponse<PropConfig>

    type LimitPropUseReq = {
        /**战局ID */
        serialNo: string
        event: string
    }

    type LimitPropUseRes = IResponse

    /**非限制性道具列表请求 */
    type GameEventsReq = {
        /**游戏场景 */
        CScene?: string
        /**游戏流水 */
        serialNo?: string
    }

    /**非限制性道具项 */
    type GameEventItem = {
        /**道具标识 */
        event: string
        /**道具名称 */
        eventName: string
        /**需要的云贝 */
        point: number
        /**小鱼干 */
        pointD10: number
    }

    /**非限制性道具列表数据 */
    type GameEventsData = {
        /**道具列表 */
        props: GameEventItem[]
        /**用户云贝 */
        userPoint: number
        /**用户小鱼干 */
        userPointD10: number
    }

    /**非限制性道具列表响应 */
    type GameEventsRes = IResponse<GameEventsData>

    /**使用非限制性道具请求 */
    type UseGameEventReq = {
        /**游戏场景 */
        CScene?: string
        /**事件类型: Egg-鸡蛋, Flower-鲜花 */
        event?: string
        /**当前画板作者UID */
        printUid?: number
        /**游戏流水 */
        serialNo?: string
    }

    /**使用非限制性道具响应 */
    type UseGameEventRes = IResponse<GameEventsData>

    /**道具配置响应 */
    type GamePropsConfigRes = GamePropsItem[]

    /**道具项 */
    type GamePropsItem = {
        /**道具ID */
        id: string
        /**道具名称 */
        name: string
        /**道具描述 */
        desc: string
        /**道具图标 */
        icon: string
        /**道具价格 */
        price: number
        /**道具类型 */
        type: string
        /**是否可用 */
        enabled: boolean
        /**道具效果描述 */
        effect?: string
        /**道具使用次数限制 */
        usageLimit?: number
    }

    /**道具使用请求 */
    type GamePropsUseReq = {
        /**战局ID */
        battleId: string
        /**道具ID */
        propsId: string
        /**目标玩家ID（如果道具需要指定目标） */
        targetPlayerId?: string
        /**额外参数 */
        params?: Record<string, any>
    }

    /**道具使用响应 */
    type GamePropsUseRes = {
        /**是否成功 */
        success: boolean
        /**消息 */
        message: string
        /**剩余使用次数 */
        remainingUses?: number
        /**效果数据 */
        effectData?: Record<string, any>
    }

    /**道具购买请求 */
    type GamePropsBuyReq = {
        /**道具ID */
        propsId: string
        /**购买数量 */
        quantity: number
        /**支付方式 */
        paymentMethod: 'coins' | 'diamonds' | 'points'
    }

    /**道具购买响应 */
    type GamePropsBuyRes = {
        /**是否成功 */
        success: boolean
        /**消息 */
        message: string
        /**购买后的道具数量 */
        totalQuantity: number
        /**剩余货币 */
        remainingCurrency: number
    }

    /**玩家道具列表请求 */
    type PlayerPropsListReq = {
        /**玩家ID */
        playerId?: string
    }

    /**玩家道具列表响应 */
    type PlayerPropsListRes = PlayerPropsItem[]

    /**玩家道具项 */
    type PlayerPropsItem = {
        /**道具ID */
        propsId: string
        /**道具名称 */
        name: string
        /**道具图标 */
        icon: string
        /**拥有数量 */
        quantity: number
        /**过期时间（如果有） */
        expireTime?: number
    }

    //#endregion

    //#region 上麦邀请

    type InviteAcceptReq = {
        roomSerial: string
    }

    type InviteRefuseReq = InviteAcceptReq

    //#endregion
}
