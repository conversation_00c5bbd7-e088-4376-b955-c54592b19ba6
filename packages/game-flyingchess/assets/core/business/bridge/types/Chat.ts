
/**
* @describe 
* <AUTHOR>
* @date 2024-09-12 11:35:02
*/

export enum RoomChangeType {
    /**房间基本信息变更 */
    TYPE_1000 = 1000,
    /**游戏状态变更 */
    TYPE_1001 = 1001,
    /**房间内麦位变化 */
    TYPE_1002 = 1002,
    /**进入房间 */
    TYPE_1004 = 1004,
    /**离开房间 */
    TYPE_1005 = 1005,
    /**房间公告变更 */
    TYPE_1009 = 1009,
    /**赠送礼物全局通知 */
    TYPE_1010 = 1010,
    /**全站礼物消息 */
    TYPE_1011 = 1011,
    /**房间内游戏结算 */
    TYPE_1020 = 1020,
    /**房间内游戏自动启动失败 */
    TYPE_1021 = 1021,
    /**被移除排麦 */
    TYPE_2001 = 2001,
    /**房间排麦被取消 */
    TYPE_2002 = 2002,
    /**被禁止麦 */
    TYPE_2012 = 2012,
    /**被解禁麦 */
    TYPE_2013 = 2013,
    /**上麦邀请 */
    TYPE_2015 = 2015,
    /**房客被踢 */
    TYPE_2031 = 2031,
    /**房管更新 */
    TYPE_2033 = 2033,
    /**未准备被踢出房间（或者游戏开局被踢了） */
    TYPE_2043 = 2043,

}

export type ChatRoomUser = IBridge.Profile

export type CustomMessage = {
    typeNew: Exclude<RoomChangeType, RoomChangeType.TYPE_1004 | RoomChangeType.TYPE_1005 | RoomChangeType.TYPE_1010>,
    data?: null,
    desc: string,
    msg: string,
    isToast: number,
} | {
    typeNew: RoomChangeType.TYPE_1004
    data: ChatRoomUser,
} | {
    typeNew: RoomChangeType.TYPE_1005
    userIds: number[],
}
    | {
        typeNew: RoomChangeType.TYPE_1010,
        data: {
            giftImage: string;
            giftName: string;
            giftNum: number;
            giftType: number;
            giver: IBridge.Profile;
            receiver: IBridge.Profile;
        }
    };

/**消息数据 */
export type ChatMessageData =
    | {
        msgType: ChatRoomMsgType.TYPE_TEXT_MSG;
        content: string;
        userId: number;
        user: IBridge.Profile;
    }
    | {
        msgType: ChatRoomMsgType.TYPE_SYSTEM_MSG;
        content: string;
    }
    | {
        msgType: ChatRoomMsgType.TYPE_GIFT_MSG;
        giftImage: string;
        giftName: string;
        giftNum: number;
        giftType: number;
        giver: IBridge.Profile;
        receiver: IBridge.Profile;
    };




export enum ChatRoomMsgType {
    TYPE_UN_KNOW = -1, //未知
    TYPE_TEXT_MSG = 0, //普通文本消息
    TYPE_IMAGE_MSG = 1, //表情图片消息
    TYPE_SYSTEM_MSG = 2, //系统消息
    TYPE_GIFT_MSG = 3, //礼物消息
}